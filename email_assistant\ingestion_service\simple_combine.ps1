# Simple file combination using copy command (memory efficient)
$inputDir = "F:\EMAILS\full"
$outputFile = "F:\EMAILS\full_combined_all.mbox"

Write-Host "=== Simple File Combination (Memory Efficient) ==="
Write-Host "Input directory: $inputDir"
Write-Host "Output file: $outputFile"
Write-Host ""

# Remove existing output file
if (Test-Path $outputFile) {
    Remove-Item $outputFile
    Write-Host "Removed existing output file"
}

# Get all files in chronological order
$files = @(
    "2010d", "2011d", "2012d", "2013d", "2014d", "2015d", 
    "2016d", "2017d", "2018d", "2019d", "2020d", "2021d", 
    "2022d", "2023d", "2024d", "2025Id", "2025Sd"
)

Write-Host "Combining files using copy command..."

$fileList = @()
foreach ($file in $files) {
    $filePath = Join-Path $inputDir $file
    if (Test-Path $filePath) {
        $fileList += "`"$filePath`""
        $sizeMB = [math]::Round((Get-Item $filePath).Length / 1MB, 1)
        Write-Host "  - $file ($sizeMB MB)"
    }
}

Write-Host ""
Write-Host "Executing copy command..."

# Use copy command to combine all files efficiently
$copyCommand = "copy /b " + ($fileList -join " + ") + " `"$outputFile`""
Write-Host "Command: $copyCommand"

# Execute the copy command
cmd /c $copyCommand

if (Test-Path $outputFile) {
    $finalSize = (Get-Item $outputFile).Length
    $finalSizeMB = [math]::Round($finalSize / 1MB, 1)
    $finalSizeGB = [math]::Round($finalSize / 1GB, 2)
    
    Write-Host ""
    Write-Host "=== COMBINATION COMPLETE ==="
    Write-Host "[OK] Combined file: $outputFile"
    Write-Host "[OK] Final size: $finalSizeMB MB ($finalSizeGB GB)"
    Write-Host "[OK] All your cleaned email files are now combined!"
    Write-Host ""
    Write-Host "Next step: Process with thunderbird_processor for threading..."
} else {
    Write-Host "[ERROR] Failed to create combined file"
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
