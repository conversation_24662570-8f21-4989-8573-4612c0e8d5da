// AI-Assisted Email Response System - Email Detail Component

import { invoke } from '@tauri-apps/api/core';
import type { EmailDetailResponse, EmailSummary, ResponseContext } from '../types';

export class EmailDetail {
  private container: HTMLElement;
  private email: EmailDetailResponse | null = null;
  private loading: boolean = false;
  private onGenerateDraft: ((emailId: string) => void) | null = null;
  private onGenerateInformation: ((emailId: string, responseContext: string) => void) | null = null;
  private currentResponseContext: ResponseContext['id'] = 'general';

  private readonly responseContexts: ResponseContext[] = [
    {
      id: 'general',
      label: 'General Information',
      description: 'General legal information and research'
    },
    {
      id: 'lawyer-to-insurance',
      label: 'Lawyer to Insurance',
      description: 'Formal communication to insurance companies'
    },
    {
      id: 'insurance-to-lawyer',
      label: 'Insurance to Lawyer',
      description: 'Business communication from insurance perspective'
    }
  ];

  constructor(container: HTMLElement) {
    this.container = container;
    this.render();
  }

  public setOnGenerateDraft(callback: (emailId: string) => void) {
    this.onGenerateDraft = callback;
  }

  public setOnGenerateInformation(callback: (emailId: string, responseContext: string) => void) {
    this.onGenerateInformation = callback;
  }

  public async loadEmail(emailSummary: EmailSummary) {
    this.setLoading(true);
    try {
      const response: EmailDetailResponse = await invoke('get_email_details', {
        emailId: emailSummary.id
      });
      
      this.email = response;
      this.renderEmailDetail();
    } catch (error) {
      this.renderError(`Failed to load email details: ${error}`);
    } finally {
      this.setLoading(false);
    }
  }

  private setLoading(loading: boolean) {
    this.loading = loading;
    this.renderLoadingState();
  }

  private render() {
    this.container.innerHTML = `
      <div class="email-detail-container">
        <div class="email-detail-header">
          <h2>Email Details</h2>
          <div class="email-detail-controls">
            <div class="response-context-selector" style="display: none;">
              <label for="email-response-context">Response Context:</label>
              <select id="email-response-context" class="context-select">
                ${this.responseContexts.map(ctx => `
                  <option value="${ctx.id}" ${ctx.id === this.currentResponseContext ? 'selected' : ''}>
                    ${ctx.label}
                  </option>
                `).join('')}
              </select>
            </div>
          </div>
          <div class="email-detail-actions">
            <button id="generate-info-btn" class="btn btn-secondary" style="display: none;">
              <span class="icon">📋</span>
              Get Information
            </button>
            <button id="generate-draft-btn" class="btn btn-primary" style="display: none;">
              <span class="icon">✨</span>
              Generate Draft Reply
            </button>
          </div>
        </div>
        <div class="email-detail-content">
          <div id="email-detail-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading email details...</p>
          </div>
          <div id="email-detail-error" class="error-state" style="display: none;"></div>
          <div id="email-detail-info" class="email-detail-info" style="display: none;"></div>
        </div>
      </div>
    `;

    // Add event listeners
    const generateBtn = this.container.querySelector('#generate-draft-btn') as HTMLButtonElement;
    const generateInfoBtn = this.container.querySelector('#generate-info-btn') as HTMLButtonElement;
    const contextSelect = this.container.querySelector('#email-response-context') as HTMLSelectElement;

    generateBtn?.addEventListener('click', () => {
      if (this.email && this.onGenerateDraft) {
        this.onGenerateDraft(this.email.id);
      }
    });

    generateInfoBtn?.addEventListener('click', () => {
      if (this.email && this.onGenerateInformation) {
        this.onGenerateInformation(this.email.id, this.currentResponseContext);
      }
    });

    contextSelect?.addEventListener('change', () => {
      this.currentResponseContext = contextSelect.value as ResponseContext['id'];
    });
  }

  private renderLoadingState() {
    const loadingEl = this.container.querySelector('#email-detail-loading') as HTMLElement;
    const contentEl = this.container.querySelector('#email-detail-info') as HTMLElement;
    const errorEl = this.container.querySelector('#email-detail-error') as HTMLElement;
    
    if (this.loading) {
      loadingEl.style.display = 'flex';
      contentEl.style.display = 'none';
      errorEl.style.display = 'none';
    } else {
      loadingEl.style.display = 'none';
    }
  }

  private renderError(message: string) {
    const errorEl = this.container.querySelector('#email-detail-error') as HTMLElement;
    const contentEl = this.container.querySelector('#email-detail-info') as HTMLElement;
    const generateBtn = this.container.querySelector('#generate-draft-btn') as HTMLElement;
    
    errorEl.innerHTML = `
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${message}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `;
    
    errorEl.style.display = 'block';
    contentEl.style.display = 'none';
    generateBtn.style.display = 'none';
  }

  private renderEmailDetail() {
    if (!this.email) return;

    const infoEl = this.container.querySelector('#email-detail-info') as HTMLElement;
    const errorEl = this.container.querySelector('#email-detail-error') as HTMLElement;
    const generateBtn = this.container.querySelector('#generate-draft-btn') as HTMLElement;
    const generateInfoBtn = this.container.querySelector('#generate-info-btn') as HTMLElement;
    const contextSelector = this.container.querySelector('.response-context-selector') as HTMLElement;
    
    errorEl.style.display = 'none';
    
    const sentDate = this.email.sent_date 
      ? new Date(this.email.sent_date).toLocaleString() 
      : 'Unknown';
    const createdDate = new Date(this.email.created_at).toLocaleString();
    const subject = this.email.subject || '(No subject)';
    const fromAddress = this.email.from_address || 'Unknown sender';
    const content = this.email.plain_text_content || 'No content available';

    infoEl.innerHTML = `
      <div class="email-metadata">
        <div class="metadata-row">
          <label>Subject:</label>
          <span class="email-subject">${this.escapeHtml(subject)}</span>
        </div>
        <div class="metadata-row">
          <label>From:</label>
          <span class="email-from">${this.escapeHtml(fromAddress)}</span>
        </div>
        <div class="metadata-row">
          <label>To:</label>
          <span class="email-to">${this.email.to_addresses.map(addr => this.escapeHtml(addr)).join(', ')}</span>
        </div>
        <div class="metadata-row">
          <label>Sent:</label>
          <span class="email-sent-date">${sentDate}</span>
        </div>
        <div class="metadata-row">
          <label>Processed:</label>
          <span class="email-created-date">${createdDate}</span>
        </div>
        ${this.email.file_path ? `
        <div class="metadata-row">
          <label>Source File:</label>
          <span class="email-file-path">${this.escapeHtml(this.email.file_path)}</span>
        </div>
        ` : ''}
      </div>
      <div class="email-content">
        <h3>Email Content</h3>
        <div class="email-body">
          <pre class="email-text">${this.escapeHtml(content)}</pre>
        </div>
      </div>
    `;

    infoEl.style.display = 'block';
    generateBtn.style.display = 'inline-flex';
    generateInfoBtn.style.display = 'inline-flex';
    contextSelector.style.display = 'block';
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  public clear() {
    this.email = null;
    const infoEl = this.container.querySelector('#email-detail-info') as HTMLElement;
    const errorEl = this.container.querySelector('#email-detail-error') as HTMLElement;
    const generateBtn = this.container.querySelector('#generate-draft-btn') as HTMLElement;
    const generateInfoBtn = this.container.querySelector('#generate-info-btn') as HTMLElement;
    const contextSelector = this.container.querySelector('.response-context-selector') as HTMLElement;
    
    infoEl.style.display = 'none';
    errorEl.style.display = 'none';
    generateBtn.style.display = 'none';
    generateInfoBtn.style.display = 'none';
    contextSelector.style.display = 'none';
    
    infoEl.innerHTML = `
      <div class="empty-state">
        <span class="icon">📧</span>
        <h3>Select an email</h3>
        <p>Choose an email from the list to view its details.</p>
      </div>
    `;
    infoEl.style.display = 'block';
  }

  public getCurrentEmail(): EmailDetailResponse | null {
    return this.email;
  }
}
