[package]
name = "ingestion_service"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "ingestion_service"
path = "src/main.rs"

[[bin]]
name = "ingestion_server"
path = "src/server.rs"

[[bin]]
name = "thunderbird_processor"
path = "src/bin/thunderbird_processor.rs"

[[bin]]
name = "debug_thunderbird"
path = "src/bin/debug_thunderbird.rs"

[[bin]]
name = "mail_cleaner"
path = "src/bin/mail_cleaner.rs"

[lib]
name = "ingestion_service"
path = "src/lib.rs"

[dependencies]
html2text = "0.5"
mail-parser = { version = "0.11", features = ["full_encoding"] }
tokio = { version = "1.37", features = ["full"] }
qdrant-client = "1.7"
serde_json = "1.0"
uuid = { version = "1.8", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.11", features = ["json"] }
dotenv = "0.15"
tempfile = "3.8"
axum = { version = "0.7", features = ["macros"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
tracing = "0.1"
tracing-subscriber = "0.3"
sha2 = "0.10"
# Document processing for DOC/DOCX extraction
docx-rs = "0.4"
zip = "0.6"
base64 = "0.22"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["processthreadsapi", "psapi", "winnt"] }
