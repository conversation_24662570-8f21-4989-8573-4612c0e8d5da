@echo off
REM Thunderbird Email Processor - Standalone Runner
REM Usage: run_processor.bat "input_directory" "output_file.mbox"

echo ========================================
echo Thunderbird Email Processor
echo ========================================
echo.

if "%~2"=="" (
    echo Usage: %0 "input_directory" "output_file.mbox"
    echo.
    echo Examples:
    echo   %0 "C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders" "processed_emails.mbox"
    echo   %0 "D:\Thunderbird\Backup\Mail" "my_emails.mbox"
    echo.
    echo Arguments:
    echo   input_directory   - Path to Thunderbird profile directory or folder containing .mbox/.sbd files
    echo   output_file.mbox  - Name for the processed output file
    echo.
    pause
    exit /b 1
)

set INPUT_DIR=%~1
set OUTPUT_FILE=%~2

echo Input Directory: %INPUT_DIR%
echo Output File: %OUTPUT_FILE%
echo.

REM Check if input directory exists
if not exist "%INPUT_DIR%" (
    echo ERROR: Input directory does not exist: %INPUT_DIR%
    echo.
    pause
    exit /b 1
)

REM Check if the executable exists
if not exist "target\release\thunderbird_processor.exe" (
    echo ERROR: Thunderbird processor executable not found.
    echo Please build the project first with: cargo build --release --bin thunderbird_processor
    echo.
    pause
    exit /b 1
)

echo Starting email processing...
echo.

REM Run the processor
"target\release\thunderbird_processor.exe" "%INPUT_DIR%" "%OUTPUT_FILE%"

set RESULT=%ERRORLEVEL%

echo.
if %RESULT%==0 (
    echo ========================================
    echo Processing completed successfully!
    echo ========================================
    echo.
    echo Output file: %OUTPUT_FILE%
    
    REM Show file size if it exists
    if exist "%OUTPUT_FILE%" (
        for %%A in ("%OUTPUT_FILE%") do (
            echo File size: %%~zA bytes
        )
    )
    
    echo.
    echo You can now use this file with the AI Email Assistant ingestion service.
    echo.
) else (
    echo ========================================
    echo Processing failed with error code: %RESULT%
    echo ========================================
    echo.
    echo Please check the error messages above and try again.
    echo.
)

pause
