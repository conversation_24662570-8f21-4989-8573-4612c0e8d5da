"""
Comprehensive Integration Test Suite

This module provides integration tests that validate the complete system workflow
including service communication, data flow, and end-to-end functionality.
"""

import pytest
import asyncio
import httpx
import json
import time
import os
import sys
from pathlib import Path

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "rag_service"))

from rag_service.compliance_logger import get_compliance_logger
from rag_service.pseudonymizer import get_pseudonymizer


class TestServiceIntegration:
    """Integration tests for service communication"""
    
    @pytest.fixture
    def service_urls(self):
        """Service endpoint URLs"""
        return {
            "ingestion": "http://localhost:8080",
            "rag": "http://localhost:8003"
        }
    
    @pytest.mark.asyncio
    async def test_service_health_checks(self, service_urls):
        """Test that all services are healthy"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            for service_name, url in service_urls.items():
                try:
                    response = await client.get(f"{url}/health")
                    assert response.status_code == 200
                    health_data = response.json()
                    assert "status" in health_data
                    print(f"✓ {service_name} service is healthy: {health_data}")
                except httpx.ConnectError:
                    pytest.skip(f"{service_name} service not available at {url}")
    
    @pytest.mark.asyncio
    async def test_ingestion_service_basic_functionality(self, service_urls):
        """Test basic ingestion service functionality"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Test email ingestion
            test_email = {
                "subject": "Integration Test Email",
                "sender": "<EMAIL>",
                "content": "This is a test email for integration testing purposes.",
                "timestamp": "2024-01-01 10:00:00"
            }
            
            try:
                response = await client.post(
                    f"{service_urls['ingestion']}/ingest",
                    json=test_email
                )
                assert response.status_code in [200, 201]
                result = response.json()
                assert "message_id" in result or "id" in result
                print(f"✓ Email ingested successfully: {result}")
                
            except httpx.ConnectError:
                pytest.skip("Ingestion service not available")
    
    @pytest.mark.asyncio
    async def test_rag_service_basic_functionality(self, service_urls):
        """Test basic RAG service functionality"""
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Test draft generation
            test_request = {
                "subject": "Integration Test Legal Inquiry",
                "sender": "<EMAIL>",
                "content": "I need legal advice regarding contract terms and conditions."
            }
            
            try:
                response = await client.post(
                    f"{service_urls['rag']}/generate_draft",
                    json=test_request
                )
                assert response.status_code == 200
                result = response.json()
                
                # Validate response structure
                assert "draft" in result
                assert "context_emails_count" in result
                assert "similar_emails" in result
                assert "metadata" in result
                
                # Validate content
                assert len(result["draft"]) > 0
                assert isinstance(result["context_emails_count"], int)
                assert isinstance(result["similar_emails"], list)
                assert isinstance(result["metadata"], dict)
                
                print(f"✓ Draft generated successfully: {len(result['draft'])} characters")
                print(f"✓ Context emails used: {result['context_emails_count']}")
                
            except httpx.ConnectError:
                pytest.skip("RAG service not available")
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, service_urls):
        """Test complete end-to-end workflow"""
        async with httpx.AsyncClient(timeout=90.0) as client:
            try:
                # Step 1: Ingest a test email
                test_email = {
                    "subject": "E2E Test - Contract Review Request",
                    "sender": "<EMAIL>",
                    "content": "I need assistance with reviewing a software licensing contract. The contract includes terms for intellectual property, liability limitations, and termination clauses.",
                    "timestamp": "2024-01-01 14:30:00"
                }
                
                ingest_response = await client.post(
                    f"{service_urls['ingestion']}/ingest",
                    json=test_email
                )
                assert ingest_response.status_code in [200, 201]
                ingest_result = ingest_response.json()
                print(f"✓ Step 1 - Email ingested: {ingest_result}")
                
                # Wait a moment for indexing
                await asyncio.sleep(2)
                
                # Step 2: Generate a draft response
                draft_request = {
                    "subject": "Re: Contract Review Request",
                    "sender": "<EMAIL>",
                    "content": "Thank you for your contract review request. I need to analyze the licensing terms you mentioned."
                }
                
                draft_response = await client.post(
                    f"{service_urls['rag']}/generate_draft",
                    json=draft_request
                )
                assert draft_response.status_code == 200
                draft_result = draft_response.json()
                
                # Validate the complete workflow
                assert "draft" in draft_result
                assert len(draft_result["draft"]) > 0
                
                print(f"✓ Step 2 - Draft generated: {len(draft_result['draft'])} characters")
                print(f"✓ E2E workflow completed successfully")
                
                return {
                    "ingest_result": ingest_result,
                    "draft_result": draft_result
                }
                
            except httpx.ConnectError as e:
                pytest.skip(f"Service not available: {e}")


class TestComplianceIntegration:
    """Integration tests for compliance features"""
    
    @pytest.mark.asyncio
    async def test_compliance_logging_integration(self):
        """Test that compliance logging works in integration scenarios"""
        # Get compliance logger
        logger = get_compliance_logger()
        transaction_id = logger.generate_transaction_id()
        
        # Test logging workflow
        request_data = {
            "subject": "Integration Test Compliance",
            "sender": "<EMAIL>",
            "content": "Testing compliance logging integration"
        }
        
        # Log various events
        logger.log_draft_request(transaction_id, request_data)
        
        embedding_info = {"dimension": 384, "model": "test-model"}
        retrieved_emails = [{"id": "test_1", "similarity_score": 0.8}]
        search_metadata = {"similarity_threshold": 0.7}
        
        logger.log_context_retrieval(transaction_id, embedding_info, retrieved_emails, search_metadata)
        
        # Verify logs were created
        log_file = Path("logs/compliance_audit.log")
        if log_file.exists():
            with open(log_file, 'r') as f:
                log_content = f.read()
                assert transaction_id in log_content
                assert "draft_request" in log_content
                assert "context_retrieval" in log_content
                print("✓ Compliance logging integration successful")
        else:
            print("⚠️ Compliance log file not found (may be expected in test environment)")
    
    def test_pseudonymisation_integration(self):
        """Test pseudonymisation integration"""
        # Test with different configurations
        os.environ["PSEUDONYMISATION_ENABLED"] = "true"
        os.environ["PSEUDONYMISATION_LEVEL"] = "basic"
        
        # Import after setting environment
        from rag_service.pseudonymizer import PseudonymisationConfig, Pseudonymizer
        
        config = PseudonymisationConfig()
        config.rules = config._initialize_rules()
        pseudonymizer = Pseudonymizer(config)
        
        test_text = "<NAME_EMAIL> for contract details."
        result = pseudonymizer.pseudonymize_text(test_text)
        
        if pseudonymizer.config.enabled:
            assert "<EMAIL>" not in result
            assert "[EMAIL_" in result
            print("✓ Pseudonymisation integration successful")
        else:
            print("ℹ️ Pseudonymisation disabled in test environment")


class TestPerformanceIntegration:
    """Integration tests for performance characteristics"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test system behavior under concurrent load"""
        service_url = "http://localhost:8003"
        
        async def make_request(session_id):
            async with httpx.AsyncClient(timeout=60.0) as client:
                request = {
                    "subject": f"Concurrent Test {session_id}",
                    "sender": f"concurrent-test-{session_id}@example.com",
                    "content": f"This is concurrent test request number {session_id}"
                }
                
                try:
                    response = await client.post(
                        f"{service_url}/generate_draft",
                        json=request
                    )
                    return {
                        "session_id": session_id,
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0
                    }
                except Exception as e:
                    return {
                        "session_id": session_id,
                        "success": False,
                        "error": str(e)
                    }
        
        # Run 5 concurrent requests
        try:
            tasks = [make_request(i) for i in range(5)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful_requests = [r for r in results if isinstance(r, dict) and r.get("success")]
            success_rate = len(successful_requests) / len(results)
            
            print(f"✓ Concurrent requests test: {len(successful_requests)}/{len(results)} successful")
            print(f"✓ Success rate: {success_rate * 100:.1f}%")
            
            assert success_rate >= 0.8, f"Success rate too low: {success_rate}"
            
        except Exception as e:
            pytest.skip(f"Concurrent test failed: {e}")
    
    @pytest.mark.asyncio
    async def test_response_time_baseline(self):
        """Test response time baseline"""
        service_url = "http://localhost:8003"
        
        request = {
            "subject": "Performance Baseline Test",
            "sender": "<EMAIL>",
            "content": "This is a performance baseline test to measure response times."
        }
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                start_time = time.time()
                response = await client.post(
                    f"{service_url}/generate_draft",
                    json=request
                )
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                
                assert response.status_code == 200
                assert response_time < 30000  # Should complete within 30 seconds
                
                print(f"✓ Response time baseline: {response_time:.2f}ms")
                
                # Log performance baseline
                baseline_data = {
                    "test_type": "response_time_baseline",
                    "response_time_ms": response_time,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "success": True
                }
                
                # Save baseline for future comparison
                baseline_file = Path("benchmark_results/baseline_performance.json")
                baseline_file.parent.mkdir(exist_ok=True)
                
                with open(baseline_file, 'w') as f:
                    json.dump(baseline_data, f, indent=2)
                
                print(f"✓ Baseline saved to {baseline_file}")
                
        except Exception as e:
            pytest.skip(f"Performance baseline test failed: {e}")


class TestErrorHandlingIntegration:
    """Integration tests for error handling and resilience"""
    
    @pytest.mark.asyncio
    async def test_service_unavailable_handling(self):
        """Test behavior when services are unavailable"""
        # Test with non-existent service
        async with httpx.AsyncClient(timeout=5.0) as client:
            try:
                response = await client.post(
                    "http://localhost:9999/generate_draft",
                    json={"subject": "test", "sender": "test", "content": "test"}
                )
                # Should not reach here
                assert False, "Expected connection error"
            except httpx.ConnectError:
                print("✓ Correctly handles service unavailable")
    
    @pytest.mark.asyncio
    async def test_invalid_request_handling(self):
        """Test handling of invalid requests"""
        service_url = "http://localhost:8003"
        
        invalid_requests = [
            {},  # Empty request
            {"subject": "test"},  # Missing required fields
            {"subject": "", "sender": "", "content": ""},  # Empty fields
            {"subject": "x" * 10000, "sender": "test", "content": "test"}  # Very long subject
        ]
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                for i, invalid_request in enumerate(invalid_requests):
                    try:
                        response = await client.post(
                            f"{service_url}/generate_draft",
                            json=invalid_request
                        )
                        # Should handle gracefully (either 400 error or successful with fallback)
                        assert response.status_code in [200, 400, 422]
                        print(f"✓ Invalid request {i+1} handled gracefully: {response.status_code}")
                    except Exception as e:
                        print(f"✓ Invalid request {i+1} handled with exception: {type(e).__name__}")
        except Exception as e:
            pytest.skip(f"Error handling test failed: {e}")


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "-s"])
