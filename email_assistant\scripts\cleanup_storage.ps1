# AI Email Assistant - Storage Cleanup Script
# Cleans Qdrant database and ingestion storage for fresh start

param(
    [switch]$Force,
    [switch]$KeepLogs,
    [switch]$KeepBackups,
    [switch]$DryRun
)

# Configuration
$ProjectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$QdrantUrl = "http://localhost:6334"
$QdrantStoragePath = Join-Path $ProjectRoot "ingestion_service\qdrant\storage"
$IngestionPath = Join-Path $ProjectRoot "ingestion_service"
$LogsPath = Join-Path $ProjectRoot "logs"
$RagLogsPath = Join-Path $ProjectRoot "rag_service\logs"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-QdrantRunning {
    try {
        $response = Invoke-RestMethod -Uri "$QdrantUrl/collections" -Method GET -TimeoutSec 5
        return $true
    }
    catch {
        return $false
    }
}

function Stop-Services {
    Write-ColorOutput "🛑 Stopping services..." $Yellow
    
    # Stop Qdrant if running
    $qdrantProcess = Get-Process -Name "qdrant" -ErrorAction SilentlyContinue
    if ($qdrantProcess) {
        Write-ColorOutput "  Stopping Qdrant..." $Yellow
        Stop-Process -Name "qdrant" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 3
    }
    
    # Stop ingestion service if running
    $ingestionProcess = Get-Process -Name "ingestion_service" -ErrorAction SilentlyContinue
    if ($ingestionProcess) {
        Write-ColorOutput "  Stopping Ingestion Service..." $Yellow
        Stop-Process -Name "ingestion_service" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
    }
    
    Write-ColorOutput "✓ Services stopped" $Green
}

function Clear-QdrantDatabase {
    Write-ColorOutput "🗄️ Clearing Qdrant database..." $Yellow
    
    if (Test-QdrantRunning) {
        try {
            # Get all collections
            $collections = Invoke-RestMethod -Uri "$QdrantUrl/collections" -Method GET
            
            foreach ($collection in $collections.result.collections) {
                $collectionName = $collection.name
                Write-ColorOutput "  Deleting collection: $collectionName" $Yellow
                
                if (-not $DryRun) {
                    Invoke-RestMethod -Uri "$QdrantUrl/collections/$collectionName" -Method DELETE
                }
            }
            
            Write-ColorOutput "✓ Qdrant collections cleared" $Green
        }
        catch {
            Write-ColorOutput "⚠ Failed to clear Qdrant via API: $($_.Exception.Message)" $Red
            Write-ColorOutput "  Will clear storage files instead..." $Yellow
        }
    } else {
        Write-ColorOutput "  Qdrant not running, will clear storage files..." $Yellow
    }
}

function Clear-QdrantStorage {
    Write-ColorOutput "📁 Clearing Qdrant storage files..." $Yellow
    
    if (Test-Path $QdrantStoragePath) {
        $storageSize = (Get-ChildItem $QdrantStoragePath -Recurse | Measure-Object -Property Length -Sum).Sum
        $storageSizeMB = [math]::Round($storageSize / 1MB, 2)
        
        Write-ColorOutput "  Storage size: $storageSizeMB MB" $Cyan
        
        if (-not $DryRun) {
            Remove-Item $QdrantStoragePath -Recurse -Force -ErrorAction SilentlyContinue
            Write-ColorOutput "✓ Qdrant storage cleared ($storageSizeMB MB freed)" $Green
        } else {
            Write-ColorOutput "  [DRY RUN] Would remove $storageSizeMB MB" $Cyan
        }
    } else {
        Write-ColorOutput "  No Qdrant storage found" $Cyan
    }
}

function Clear-IngestionStorage {
    Write-ColorOutput "📧 Clearing ingestion storage..." $Yellow
    
    $filesToClean = @(
        "*.mbox",
        "*.eml",
        "test_*",
        "f_*_*.mbox",
        "storage_backup_*"
    )
    
    $totalSize = 0
    $fileCount = 0
    
    foreach ($pattern in $filesToClean) {
        $files = Get-ChildItem -Path $IngestionPath -Filter $pattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            $totalSize += $file.Length
            $fileCount++
            
            Write-ColorOutput "  Removing: $($file.Name)" $Yellow
            
            if (-not $DryRun) {
                Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
            }
        }
    }
    
    # Clean directories
    $dirsToClean = @("test_*", "storage_backup_*")
    foreach ($pattern in $dirsToClean) {
        $dirs = Get-ChildItem -Path $IngestionPath -Directory -Filter $pattern -ErrorAction SilentlyContinue
        foreach ($dir in $dirs) {
            $dirSize = (Get-ChildItem $dir.FullName -Recurse | Measure-Object -Property Length -Sum).Sum
            $totalSize += $dirSize
            
            Write-ColorOutput "  Removing directory: $($dir.Name)" $Yellow
            
            if (-not $DryRun) {
                Remove-Item $dir.FullName -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
    }
    
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    
    if ($DryRun) {
        Write-ColorOutput "  [DRY RUN] Would remove $fileCount files ($totalSizeMB MB)" $Cyan
    } else {
        Write-ColorOutput "✓ Ingestion storage cleared: $fileCount files ($totalSizeMB MB freed)" $Green
    }
}

function Clear-Logs {
    if ($KeepLogs) {
        Write-ColorOutput "📋 Keeping logs (--KeepLogs specified)" $Cyan
        return
    }
    
    Write-ColorOutput "📋 Clearing logs..." $Yellow
    
    $logPaths = @($LogsPath, $RagLogsPath)
    $totalSize = 0
    $fileCount = 0
    
    foreach ($logPath in $logPaths) {
        if (Test-Path $logPath) {
            $logFiles = Get-ChildItem $logPath -Recurse -File
            foreach ($file in $logFiles) {
                $totalSize += $file.Length
                $fileCount++
                
                Write-ColorOutput "  Removing: $($file.FullName)" $Yellow
                
                if (-not $DryRun) {
                    Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
                }
            }
        }
    }
    
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    
    if ($DryRun) {
        Write-ColorOutput "  [DRY RUN] Would remove $fileCount log files ($totalSizeMB MB)" $Cyan
    } else {
        Write-ColorOutput "✓ Logs cleared: $fileCount files ($totalSizeMB MB freed)" $Green
    }
}

function Clear-BuildArtifacts {
    Write-ColorOutput "🔨 Clearing build artifacts..." $Yellow
    
    $targetPath = Join-Path $IngestionPath "target"
    
    if (Test-Path $targetPath) {
        $targetSize = (Get-ChildItem $targetPath -Recurse | Measure-Object -Property Length -Sum).Sum
        $targetSizeMB = [math]::Round($targetSize / 1MB, 2)
        
        Write-ColorOutput "  Build artifacts size: $targetSizeMB MB" $Cyan
        
        if (-not $DryRun) {
            Remove-Item $targetPath -Recurse -Force -ErrorAction SilentlyContinue
            Write-ColorOutput "✓ Build artifacts cleared ($targetSizeMB MB freed)" $Green
        } else {
            Write-ColorOutput "  [DRY RUN] Would remove $targetSizeMB MB" $Cyan
        }
    } else {
        Write-ColorOutput "  No build artifacts found" $Cyan
    }
}

# Main execution
Write-ColorOutput "🧹 AI Email Assistant - Storage Cleanup" $Cyan
Write-ColorOutput "=======================================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No files will be deleted" $Yellow
}

# Safety check
if (-not $Force -and -not $DryRun) {
    Write-ColorOutput "⚠️  WARNING: This will permanently delete all email data, logs, and database contents!" $Red
    Write-ColorOutput "   - Qdrant vector database" $Red
    Write-ColorOutput "   - All processed email files (.mbox)" $Red
    Write-ColorOutput "   - Test files and temporary data" $Red
    Write-ColorOutput "   - Application logs" $Red
    Write-ColorOutput "   - Build artifacts" $Red
    Write-Host ""
    
    $confirmation = Read-Host "Are you sure you want to continue? Type 'YES' to confirm"
    if ($confirmation -ne "YES") {
        Write-ColorOutput "❌ Cleanup cancelled" $Yellow
        exit 0
    }
}

Write-Host ""

# Execute cleanup steps
Stop-Services
Clear-QdrantDatabase
Clear-QdrantStorage
Clear-IngestionStorage
Clear-Logs
Clear-BuildArtifacts

Write-Host ""
Write-ColorOutput "🎉 Cleanup completed successfully!" $Green
Write-ColorOutput "   You can now restart services with a clean slate." $Green

if (-not $DryRun) {
    Write-Host ""
    Write-ColorOutput "💡 To restart services:" $Cyan
    Write-ColorOutput "   .\scripts\orchestrate_services.ps1 -Action Start" $Cyan
}
