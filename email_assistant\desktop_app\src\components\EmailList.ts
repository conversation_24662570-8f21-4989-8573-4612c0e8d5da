// AI-Assisted Email Response System - Email List Component

import { invoke } from '@tauri-apps/api/core';
import type { EmailSummary, EmailListResponse, EmailDetailResponse, ConversationThreadResponse } from '../types';

export class EmailList {
  private container: HTMLElement;
  private emails: EmailSummary[] = [];
  private loading: boolean = false;
  private pageSize: number = 20;
  private totalCount: number = 0;
  private selectedEmailId: string | null = null;
  private onEmailSelect: ((email: EmailSummary) => void) | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.render();
    this.loadEmails();
  }

  public setOnEmailSelect(callback: (email: EmailSummary) => void) {
    this.onEmailSelect = callback;
  }

  private async loadEmails() {
    this.setLoading(true);
    try {
      // Load all emails by fetching all pages
      await this.loadAllEmails();
      this.renderEmailList();
    } catch (error) {
      this.renderError(`Failed to load emails: ${error}`);
    } finally {
      this.setLoading(false);
    }
  }

  private async loadAllEmails() {
    const allEmails: EmailSummary[] = [];
    let currentPage = 1;
    let totalPages = 1;

    do {
      const response: EmailListResponse = await invoke('get_processed_emails', {
        page: currentPage,
        pageSize: this.pageSize
      });

      allEmails.push(...response.emails);
      this.totalCount = response.total_count;
      totalPages = Math.ceil(response.total_count / response.page_size);
      currentPage++;

      // Update loading state to show progress
      this.updateLoadingProgress(allEmails.length, response.total_count);

    } while (currentPage <= totalPages);

    this.emails = allEmails;
  }

  private updateLoadingProgress(loaded: number, total: number) {
    const loadingEl = this.container.querySelector('#email-list-loading p') as HTMLElement;
    if (loadingEl) {
      loadingEl.textContent = `Loading emails... ${loaded}/${total}`;
    }
  }

  private setLoading(loading: boolean) {
    this.loading = loading;
    this.renderLoadingState();
  }

  private render() {
    this.container.innerHTML = `
      <div class="email-list-container">
        <div class="email-list-header">
          <h2>Inbox Emails</h2>
          <div class="email-list-controls">
            <span class="sort-indicator">📅 Newest First</span>
            <span class="filter-indicator">📧 Inbox Only</span>
            <button id="refresh-emails" class="btn btn-secondary">
              <span class="icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
        <div class="email-list-content">
          <div id="email-list-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading emails...</p>
          </div>
          <div id="email-list-error" class="error-state" style="display: none;"></div>
          <div id="email-list-items" class="email-list-items"></div>
          <div id="email-list-pagination" class="pagination-container"></div>
        </div>
      </div>
    `;

    // Add event listeners
    const refreshBtn = this.container.querySelector('#refresh-emails') as HTMLButtonElement;
    refreshBtn?.addEventListener('click', () => this.loadEmails());
  }

  private renderLoadingState() {
    const loadingEl = this.container.querySelector('#email-list-loading') as HTMLElement;
    const contentEl = this.container.querySelector('#email-list-items') as HTMLElement;
    
    if (this.loading) {
      loadingEl.style.display = 'flex';
      contentEl.style.display = 'none';
    } else {
      loadingEl.style.display = 'none';
      contentEl.style.display = 'block';
    }
  }

  private renderError(message: string) {
    const errorEl = this.container.querySelector('#email-list-error') as HTMLElement;
    const contentEl = this.container.querySelector('#email-list-items') as HTMLElement;
    
    errorEl.innerHTML = `
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${message}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `;
    
    errorEl.style.display = 'block';
    contentEl.style.display = 'none';
  }

  private renderEmailList() {
    const listEl = this.container.querySelector('#email-list-items') as HTMLElement;
    const errorEl = this.container.querySelector('#email-list-error') as HTMLElement;

    errorEl.style.display = 'none';

    if (this.emails.length === 0) {
      listEl.innerHTML = `
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No emails found</h3>
          <p>Process some email files to see them here.</p>
        </div>
      `;
      return;
    }

    // Filter for inbox-only emails (exclude sent emails)
    const inboxEmails = this.emails.filter(email => {
      // Show only received emails, exclude sent emails
      return !email.email_type || email.email_type.toLowerCase() !== 'sent';
    });

    if (inboxEmails.length === 0) {
      listEl.innerHTML = `
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No inbox emails found</h3>
          <p>All processed emails appear to be sent emails. Only inbox emails are displayed here.</p>
        </div>
      `;
      return;
    }

    // Sort emails by date (newest to oldest)
    const sortedEmails = [...inboxEmails].sort((a, b) => {
      const dateA = a.sent_date ? new Date(a.sent_date).getTime() : 0;
      const dateB = b.sent_date ? new Date(b.sent_date).getTime() : 0;
      return dateB - dateA; // Newest first
    });

    // Show filtered emails (they will be scrollable)
    const emailItems = sortedEmails.map(email => this.renderEmailItem(email)).join('');
    listEl.innerHTML = emailItems;

    // Add click and double-click event listeners to email items
    listEl.querySelectorAll('.email-item').forEach((item, index) => {
      item.addEventListener('click', () => {
        this.selectEmail(sortedEmails[index]);
      });

      item.addEventListener('dblclick', () => {
        this.showEmailModal(sortedEmails[index]);
      });
    });

    this.renderPagination();
  }

  private renderEmailItem(email: EmailSummary): string {
    const isSelected = this.selectedEmailId === email.id;
    const sentDate = email.sent_date ? new Date(email.sent_date).toLocaleDateString() : 'Unknown';
    const fromAddress = email.from_address || 'Unknown sender';
    const subject = email.subject || '(No subject)';
    const preview = email.preview || 'No preview available';

    // Threading indicators
    const hasThread = email.thread_id && email.thread_id.trim() !== '';
    const isDuplicate = email.is_duplicate === true;
    const threadIndicator = hasThread ? '🧵' : '';
    const duplicateIndicator = isDuplicate ? '📋' : '';

    // Email type indicator
    const emailTypeClass = email.email_type ? `email-type-${email.email_type.toLowerCase()}` : '';

    return `
      <div class="email-item ${isSelected ? 'selected' : ''} ${emailTypeClass}" data-email-id="${email.id}" data-thread-id="${email.thread_id || ''}" data-conversation-id="${email.conversation_id || ''}">
        <div class="email-item-header">
          <div class="email-subject">
            <span class="threading-indicators">
              ${threadIndicator}${duplicateIndicator}
            </span>
            ${this.escapeHtml(subject)}
          </div>
          <div class="email-date">${sentDate}</div>
        </div>
        <div class="email-from">${this.escapeHtml(fromAddress)}</div>
        <div class="email-preview">${this.escapeHtml(preview)}</div>
        <div class="email-recipients">
          To: ${email.to_addresses.map(addr => this.escapeHtml(addr)).join(', ')}
        </div>
        ${hasThread ? `<div class="thread-info" title="Part of conversation thread">Thread ID: ${email.thread_id}</div>` : ''}
      </div>
    `;
  }

  private renderPagination() {
    const paginationEl = this.container.querySelector('#email-list-pagination') as HTMLElement;

    // Filter for inbox-only emails to get accurate count
    const inboxEmails = this.emails.filter(email => {
      return !email.email_type || email.email_type.toLowerCase() !== 'sent';
    });

    // Show filtered count
    paginationEl.innerHTML = `
      <div class="pagination">
        <span class="pagination-info">
          Showing ${inboxEmails.length} inbox emails of ${this.totalCount} total emails (sorted by date, newest first)
        </span>
      </div>
    `;

    paginationEl.style.display = 'flex';
  }

  private selectEmail(email: EmailSummary) {
    this.selectedEmailId = email.id;
    this.renderEmailList(); // Re-render to update selection
    
    if (this.onEmailSelect) {
      this.onEmailSelect(email);
    }
  }

  // Pagination methods removed since we now load all emails at once

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  public refresh() {
    this.emails = []; // Clear current emails
    this.loadEmails();
  }

  public getSelectedEmail(): EmailSummary | null {
    return this.emails.find(email => email.id === this.selectedEmailId) || null;
  }

  private async showEmailModal(email: EmailSummary) {
    try {
      // If email has a thread_id, show the conversation thread
      if (email.thread_id && email.thread_id.trim() !== '') {
        const conversationThread: ConversationThreadResponse = await invoke('get_conversation_thread', {
          threadId: email.thread_id
        });
        this.createConversationModal(conversationThread, email.id);
      } else {
        // Fallback to single email modal for emails without threads
        const emailDetail: EmailDetailResponse = await invoke('get_email_details', {
          emailId: email.id
        });
        this.createSingleEmailModal(emailDetail);
      }
    } catch (error) {
      console.error('Failed to load email/conversation details for modal:', error);
      // Show error in a simple alert for now
      alert(`Failed to load email details: ${error}`);
    }
  }

  private createSingleEmailModal(email: EmailDetailResponse) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.email-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal element
    const modal = document.createElement('div');
    modal.className = 'email-modal';

    const sentDate = email.sent_date
      ? new Date(email.sent_date).toLocaleString()
      : 'Unknown';
    const subject = email.subject || '(No subject)';
    const fromAddress = email.from_address || 'Unknown sender';
    const content = email.plain_text_content || 'No content available';
    const toAddresses = email.to_addresses.join(', ') || 'Unknown recipients';

    modal.innerHTML = `
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">${this.escapeHtml(subject)}</h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body">
          <div class="email-modal-metadata">
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">From:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(fromAddress)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">To:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(toAddresses)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">Date:</span>
              <span class="email-modal-metadata-value">${sentDate}</span>
            </div>
          </div>
          <div class="email-modal-content-section">
            <pre class="email-modal-text">${this.escapeHtml(content)}</pre>
          </div>
        </div>
      </div>
    `;

    // Add modal to document
    document.body.appendChild(modal);

    // Add event listeners
    const closeBtn = modal.querySelector('.email-modal-close');
    closeBtn?.addEventListener('click', () => this.closeEmailModal());

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeEmailModal();
      }
    });

    // Close on ESC key
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.closeEmailModal();
        document.removeEventListener('keydown', handleEscKey);
      }
    };
    document.addEventListener('keydown', handleEscKey);

    // Show modal with animation
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);
  }

  private createConversationModal(conversation: ConversationThreadResponse, selectedEmailId: string) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.email-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal element
    const modal = document.createElement('div');
    modal.className = 'email-modal conversation-modal';

    // Sort emails by date (oldest first for conversation flow)
    const sortedEmails = [...conversation.emails].sort((a, b) => {
      const dateA = new Date(a.sent_date).getTime();
      const dateB = new Date(b.sent_date).getTime();
      return dateA - dateB; // Oldest first
    });

    // Generate conversation thread HTML
    const conversationHtml = sortedEmails.map((email, index) => {
      const isSelected = email.id === selectedEmailId;
      const sentDate = new Date(email.sent_date).toLocaleString();
      const emailTypeClass = email.email_type ? `email-type-${email.email_type.toLowerCase()}` : '';
      const weightIndicator = email.email_weight ? `(Weight: ${Math.round(email.email_weight * 100)}%)` : '';

      return `
        <div class="conversation-email ${isSelected ? 'selected-email' : ''} ${emailTypeClass}">
          <div class="conversation-email-header">
            <div class="conversation-email-meta">
              <span class="conversation-email-position">#${index + 1}</span>
              <span class="conversation-email-type">${email.email_type}</span>
              ${email.email_weight ? `<span class="conversation-email-weight">${weightIndicator}</span>` : ''}
            </div>
            <div class="conversation-email-date">${sentDate}</div>
          </div>
          <div class="conversation-email-subject">${this.escapeHtml(email.subject)}</div>
          <div class="conversation-email-from">From: ${this.escapeHtml(email.from_address)}</div>
          <div class="conversation-email-to">To: ${email.to_addresses.map(addr => this.escapeHtml(addr)).join(', ')}</div>
          <div class="conversation-email-content">
            <pre class="conversation-email-text">${this.escapeHtml(email.cleaned_plain_text_body)}</pre>
          </div>
        </div>
      `;
    }).join('');

    modal.innerHTML = `
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">
            Conversation Thread (${conversation.total_count} emails)
            <span class="thread-id">Thread: ${conversation.thread_id}</span>
          </h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body conversation-body">
          <div class="conversation-thread">
            ${conversationHtml}
          </div>
        </div>
      </div>
    `;

    // Add modal to document
    document.body.appendChild(modal);

    // Add event listeners
    const closeBtn = modal.querySelector('.email-modal-close');
    closeBtn?.addEventListener('click', () => this.closeEmailModal());

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeEmailModal();
      }
    });

    // Close on ESC key
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.closeEmailModal();
        document.removeEventListener('keydown', handleEscKey);
      }
    };
    document.addEventListener('keydown', handleEscKey);

    // Show modal with animation
    setTimeout(() => {
      modal.classList.add('show');

      // Scroll to selected email if it exists
      const selectedEmailElement = modal.querySelector('.selected-email');
      if (selectedEmailElement) {
        selectedEmailElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 10);
  }

  private closeEmailModal() {
    const modal = document.querySelector('.email-modal');
    if (modal) {
      modal.classList.remove('show');
      setTimeout(() => {
        modal.remove();
      }, 150); // Match transition duration
    }
  }
}
