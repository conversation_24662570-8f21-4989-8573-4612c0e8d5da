# Thunderbird Email Processor - Standalone Deployment

This document provides instructions for deploying and using the standalone Thunderbird Email Processor executable.

## Quick Start

### 1. Files Included
- `thunderbird_processor.exe` - Main executable (in `target/release/`)
- `run_processor.bat` - Windows batch script for easy execution
- `run_processor.ps1` - PowerShell script with enhanced features
- `README_THUNDERBIRD_PROCESSOR.md` - Detailed documentation
- `USAGE_EXAMPLE.md` - Usage examples and troubleshooting

### 2. Simple Usage

#### Option A: Using the Batch Script (Recommended)
```cmd
run_processor.bat "C:\Path\To\Thunderbird\Profile\Mail\Local Folders" "output.mbox"
```

#### Option B: Using PowerShell Script (Enhanced)
```powershell
.\run_processor.ps1 "C:\Path\To\Thunderbird\Profile\Mail\Local Folders" "output.mbox"
```

#### Option C: Direct Executable
```cmd
target\release\thunderbird_processor.exe "C:\Path\To\Thunderbird\Profile\Mail\Local Folders" "output.mbox"
```

## Finding Your Thunderbird Profile

### Windows
1. Open Thunderbird
2. Go to Help → More Troubleshooting Information
3. Look for "Profile Folder" and click "Open Folder"
4. Navigate to `Mail\Local Folders` within that directory

**Common paths:**
```
C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\[profile-name]\Mail\Local Folders
```

### Alternative Method
1. Press `Win + R`, type `%APPDATA%\Thunderbird\Profiles`
2. Open your profile folder (usually ends with `.default`)
3. Navigate to `Mail\Local Folders`

## Example Usage

### Basic Processing
```cmd
run_processor.bat "C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders" "my_emails.mbox"
```

### Processing Specific Folders
If you have exported specific folders, you can process them individually:
```cmd
run_processor.bat "D:\Email_Backup\Inbox" "inbox_processed.mbox"
run_processor.bat "D:\Email_Backup\Sent" "sent_processed.mbox"
```

## Expected Output

The processor will display progress information:

```
Thunderbird Email Processor
===========================
Input directory: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders
Output file: my_emails.mbox

Processing Thunderbird directory: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders
Found 3 Thunderbird files to process
Processing file: C:\Users\<USER>\...\Inbox (type: Inbox)
  Parsed 1247 emails from C:\Users\<USER>\...\Inbox
Processing file: C:\Users\<USER>\...\Sent (type: Sent)
  Parsed 892 emails from C:\Users\<USER>\...\Sent
Parsed 2139 emails total
Applying email threading...
Created 847 conversation threads
Detecting duplicate emails...

=== Processing Summary ===
Total emails found: 2139
Emails processed: 2089
Duplicates removed: 50
Errors encountered: 0
Conversation threads: 847
Output file: my_emails.mbox
========================

Processing completed successfully!
```

## Output File Format

The processor creates an enhanced mbox file with:

### Standard Email Headers
- From, To, Subject, Date
- Message-ID, In-Reply-To, References

### Enhanced Threading Headers
- `X-Thread-ID`: Unique conversation identifier
- `X-Conversation-ID`: Conversation group identifier
- `X-Thread-Position`: Position in conversation
- `X-Normalized-Subject`: Cleaned subject for threading

### Email Classification Headers
- `X-Email-Type`: sent, received, draft, unknown
- `X-Email-Weight`: 0.66 for sent, 0.34 for received
- `X-Original-Folder`: Source folder path

### Processing Metadata
- `X-Content-Hash`: SHA-256 hash for duplicate detection
- `X-Is-Duplicate`: true/false duplicate flag
- `X-Processed-At`: Processing timestamp
- `X-Internal-ID`: Unique internal identifier

## Performance Expectations

### Processing Speed
- **Small mailboxes** (< 1,000 emails): 10-30 seconds
- **Medium mailboxes** (1,000-10,000 emails): 1-5 minutes
- **Large mailboxes** (10,000+ emails): 5-30 minutes

### Memory Usage
- Approximately 1-2 MB per 1,000 emails
- Peak memory usage during threading phase
- Temporary files cleaned up automatically

### File Size
- Output file typically 70-90% of input size
- Reduction due to duplicate removal and binary content filtering
- Enhanced headers add minimal overhead

## Troubleshooting

### Common Issues

#### "No Thunderbird files found"
**Cause:** Incorrect path or no .mbox/.sbd files in directory
**Solution:**
1. Verify the path points to the correct Thunderbird profile
2. Check that the directory contains email files
3. Try pointing to a specific .mbox file instead of directory

#### "Failed to parse email"
**Cause:** Corrupted email content or unsupported format
**Solution:**
1. Check the error count in the summary
2. Processing continues with other emails
3. Consider re-exporting from Thunderbird if many errors

#### "Permission denied"
**Cause:** File access restrictions or Thunderbird is running
**Solution:**
1. Close Thunderbird completely
2. Run as administrator if necessary
3. Check file permissions on the profile directory

#### High duplicate count
**Cause:** Multiple exports or overlapping folders
**Solution:**
1. Review your export process
2. Check for duplicate folder structures
3. Consider processing folders individually

### Debug Mode
For detailed logging, set environment variable:
```cmd
set RUST_LOG=debug
run_processor.bat "input_path" "output.mbox"
```

## Integration with AI Email Assistant

After processing, you can ingest the enhanced mbox file:

### 1. Start the Ingestion Service
```cmd
cd email_assistant\ingestion_service
cargo run --bin ingestion_server
```

### 2. Ingest the Processed File
```cmd
curl -X POST "http://localhost:8000/ingest/mbox" ^
     -H "Content-Type: application/json" ^
     -d "{\"file_path\": \"my_emails.mbox\"}"
```

### 3. Verify Ingestion
```cmd
curl "http://localhost:8000/messages/recent?limit=10"
```

## Deployment for Other Computers

### Minimal Deployment Package
For deployment to computers without Rust/development tools:

1. **Copy these files:**
   - `thunderbird_processor.exe`
   - `run_processor.bat` or `run_processor.ps1`
   - `README_THUNDERBIRD_PROCESSOR.md`

2. **System Requirements:**
   - Windows 10 or later
   - No additional dependencies required

3. **Installation:**
   - Extract files to any directory
   - Run from command prompt or PowerShell

### Network Deployment
For processing on multiple computers:

1. **Shared Network Drive:**
   ```cmd
   \\server\tools\thunderbird_processor\run_processor.bat "C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\*.default\Mail\Local Folders" "\\server\processed\%USERNAME%_emails.mbox"
   ```

2. **Batch Processing Script:**
   ```cmd
   for /d %%u in (C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles" (
           run_processor.bat "%%u\AppData\Roaming\Thunderbird\Profiles\*.default\Mail\Local Folders" "%%u_emails.mbox"
       )
   )
   ```

## Security Considerations

### Data Privacy
- Processing is performed locally
- No data transmitted over network
- Original files remain unchanged

### File Permissions
- Processor requires read access to Thunderbird profile
- Write access needed for output directory
- Temporary files created in system temp directory

### Antivirus Compatibility
- Executable may trigger antivirus scans
- Add to whitelist if necessary
- Source code available for verification

## Support and Updates

### Getting Help
1. Check the troubleshooting section above
2. Review the detailed documentation in `README_THUNDERBIRD_PROCESSOR.md`
3. Enable debug logging for detailed error information

### Version Information
- Current version: 1.0.0
- Built with Rust 1.70+
- Compatible with Thunderbird 78+ formats

### Updates
- Rebuild from source for latest features
- Check for updates in the main repository
- Backward compatibility maintained for output format
