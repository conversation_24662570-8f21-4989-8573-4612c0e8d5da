{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"h2\", \"http2\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 12434675677363017947, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [784494742817713399, "tower_service", false, 5062019019135106002], [1288403060204016458, "tokio_util", false, 4081414556548799092], [1788832197870803419, "hyper_rustls", false, 16715080652081116827], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [2054153378684941554, "tower_http", false, 13632272047753911532], [2517136641825875337, "sync_wrapper", false, 8905282965693471066], [2883436298747778685, "rustls_pki_types", false, 18021711389995360], [3150220818285335163, "url", false, 1492222149733532640], [5695049318159433696, "tower", false, 15472014549414805397], [5907992341687085091, "webpki_roots", false, 14623930811583027819], [5986029879202738730, "log", false, 10377209137103739149], [7620660491849607393, "futures_core", false, 4359839236934194262], [9010263965687315507, "http", false, 1862789691165185595], [9689903380558560274, "serde", false, 7295838090117490299], [10629569228670356391, "futures_util", false, 6321976705103560301], [11895591994124935963, "tokio_rustls", false, 3304537376914716630], [11957360342995674422, "hyper", false, 14768755596930326506], [12393800526703971956, "tokio", false, 6192580559177102467], [13077212702700853852, "base64", false, 16878550808635762142], [14084095096285906100, "http_body", false, 15356474674297432158], [14359893265615549706, "h2", false, 6892952181778242898], [16066129441945555748, "bytes", false, 5746740623261701503], [16400140949089969347, "rustls", false, 12746887685855653185], [16542808166767769916, "serde_urlencoded", false, 3970781345527403108], [16680807377217054954, "hyper_util", false, 16086514224213608277], [16900715236047033623, "http_body_util", false, 7220491532981291408]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-365b17496e78432b\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}