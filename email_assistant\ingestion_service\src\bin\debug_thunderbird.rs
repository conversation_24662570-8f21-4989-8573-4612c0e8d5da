// Debug utility for Thunderbird folder detection
// This helps diagnose why certain folders aren't being detected

use std::env;
use std::fs;
use std::path::Path;
use ingestion_service::is_thunderbird_mbox;

fn main() {
    let args: Vec<String> = env::args().collect();
    
    if args.len() != 2 {
        eprintln!("Usage: {} <directory_path>", args[0]);
        eprintln!("Example: {} f:\\2016.sbd", args[0]);
        std::process::exit(1);
    }
    
    let dir_path = &args[1];
    println!("Debug: Analyzing directory: {}", dir_path);
    println!("=====================================");
    
    let path = Path::new(dir_path);
    
    if !path.exists() {
        println!("ERROR: Directory does not exist: {}", dir_path);
        return;
    }
    
    if !path.is_dir() {
        println!("ERROR: Path is not a directory: {}", dir_path);
        return;
    }
    
    println!("Directory exists and is valid.");
    println!();
    
    // List all files in the directory
    match fs::read_dir(path) {
        Ok(entries) => {
            let mut file_count = 0;
            let mut dir_count = 0;
            let mut thunderbird_files = 0;
            
            println!("Contents of directory:");
            println!("----------------------");
            
            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let entry_path = entry.path();
                        let file_name = entry_path.file_name()
                            .map(|n| n.to_string_lossy())
                            .unwrap_or_default();
                        
                        if entry_path.is_dir() {
                            dir_count += 1;
                            println!("DIR:  {}", file_name);
                        } else {
                            file_count += 1;
                            let file_size = entry.metadata()
                                .map(|m| m.len())
                                .unwrap_or(0);
                            
                            let is_thunderbird = is_thunderbird_mbox(entry_path.to_str().unwrap_or(""));
                            if is_thunderbird {
                                thunderbird_files += 1;
                            }
                            
                            println!("FILE: {} ({} bytes) [Thunderbird: {}]", 
                                file_name, file_size, if is_thunderbird { "YES" } else { "NO" });
                            
                            // Show first few lines for small files
                            if file_size > 0 && file_size < 10000 {
                                if let Ok(content) = fs::read_to_string(&entry_path) {
                                    let lines: Vec<&str> = content.lines().take(3).collect();
                                    for (i, line) in lines.iter().enumerate() {
                                        if line.len() > 100 {
                                            println!("      Line {}: {}...", i + 1, &line[..100]);
                                        } else {
                                            println!("      Line {}: {}", i + 1, line);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        println!("ERROR reading entry: {}", e);
                    }
                }
            }
            
            println!();
            println!("Summary:");
            println!("--------");
            println!("Total files: {}", file_count);
            println!("Total directories: {}", dir_count);
            println!("Thunderbird files detected: {}", thunderbird_files);
            
            if thunderbird_files == 0 && file_count > 0 {
                println!();
                println!("DIAGNOSIS:");
                println!("----------");
                println!("No Thunderbird files were detected. This could be because:");
                println!("1. Files have extensions (Thunderbird mbox files typically don't)");
                println!("2. Files don't contain recognizable email headers");
                println!("3. Files are empty or corrupted");
                println!("4. Detection logic needs improvement");
                println!();
                println!("Let's check the detection criteria for each file...");
                
                // Re-scan with detailed analysis
                if let Ok(entries) = fs::read_dir(path) {
                    for entry in entries {
                        if let Ok(entry) = entry {
                            let entry_path = entry.path();
                            if entry_path.is_file() {
                                analyze_file_detection(&entry_path);
                            }
                        }
                    }
                }
            }
        }
        Err(e) => {
            println!("ERROR: Cannot read directory: {}", e);
        }
    }
}

fn analyze_file_detection(file_path: &Path) {
    let file_name = file_path.file_name()
        .map(|n| n.to_string_lossy())
        .unwrap_or_default();
    
    println!();
    println!("Analyzing file: {}", file_name);
    println!("  Path: {}", file_path.display());
    
    // Check extension
    let has_extension = file_path.extension().is_some();
    println!("  Has extension: {}", has_extension);
    if has_extension {
        println!("    Extension: {:?}", file_path.extension());
        println!("    ❌ FAIL: Thunderbird mbox files typically have no extension");
        return;
    }
    
    // Check parent directory
    let parent_dir = file_path.parent().map(|p| p.to_string_lossy().to_lowercase());
    if let Some(parent) = &parent_dir {
        println!("  Parent directory: {}", parent);
        if parent.contains(".sbd") {
            println!("    ✅ PASS: File is in .sbd directory");
        } else {
            println!("    ⚠️  INFO: File is not in .sbd directory");
        }
    }
    
    // Check file name
    let file_name_lower = file_name.to_lowercase();
    let common_names = ["inbox", "sent", "drafts", "trash", "junk", "outbox", "templates"];
    if common_names.contains(&file_name_lower.as_str()) {
        println!("    ✅ PASS: File has common Thunderbird folder name");
    } else {
        println!("    ⚠️  INFO: File name '{}' is not a common Thunderbird folder name", file_name);
    }
    
    // Check file content
    match fs::read(file_path) {
        Ok(bytes) => {
            if bytes.is_empty() {
                println!("    ❌ FAIL: File is empty");
                return;
            }
            
            println!("    File size: {} bytes", bytes.len());
            
            // Check first few lines
            let preview_size = std::cmp::min(2048, bytes.len());
            let preview = String::from_utf8_lossy(&bytes[..preview_size]);
            let lines: Vec<&str> = preview.lines().take(10).collect();
            
            println!("    First {} lines:", lines.len());
            for (i, line) in lines.iter().enumerate() {
                if line.len() > 80 {
                    println!("      {}: {}...", i + 1, &line[..80]);
                } else {
                    println!("      {}: {}", i + 1, line);
                }
            }
            
            // Check for mbox indicators
            let mut found_mbox_indicator = false;
            for line in &lines {
                if (line.starts_with("From ") && line.contains("@")) ||
                   line.starts_with("From - ") {
                    println!("    ✅ PASS: Found mbox 'From ' separator");
                    found_mbox_indicator = true;
                    break;
                }
                if line.starts_with("X-Mozilla-Status:") ||
                   line.starts_with("X-Mozilla-Status2:") {
                    println!("    ✅ PASS: Found Mozilla/Thunderbird headers");
                    found_mbox_indicator = true;
                    break;
                }
                if line.starts_with("Return-Path:") ||
                   line.starts_with("Delivered-To:") ||
                   line.starts_with("Received:") ||
                   line.starts_with("Message-ID:") {
                    println!("    ✅ PASS: Found standard email headers");
                    found_mbox_indicator = true;
                    break;
                }
            }
            
            if !found_mbox_indicator {
                println!("    ❌ FAIL: No recognizable email headers found");
                println!("    This file may not be a valid mbox file or may be corrupted");
            }
        }
        Err(e) => {
            println!("    ❌ FAIL: Cannot read file: {}", e);
        }
    }
}
