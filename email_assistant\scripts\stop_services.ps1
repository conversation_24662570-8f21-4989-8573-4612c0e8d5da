# Service Stop Script for Windows
# AI-Assisted Email Response System

Write-Host "Stopping AI Email Assistant Services..." -ForegroundColor Yellow

$projectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$jobsFile = "$projectRoot\config\service_jobs.json"

# Stop services by job ID if available
if (Test-Path $jobsFile) {
    try {
        $jobs = Get-Content $jobsFile | ConvertFrom-Json
        
        if ($jobs.RagJobId) {
            Write-Host "Stopping RAG Service (Job ID: $($jobs.RagJobId))..." -ForegroundColor Yellow
            Stop-Job -Id $jobs.RagJobId -ErrorAction SilentlyContinue
            Remove-Job -Id $jobs.RagJobId -ErrorAction SilentlyContinue
        }
        
        if ($jobs.IngestionJobId) {
            Write-Host "Stopping Ingestion Service (Job ID: $($jobs.IngestionJobId))..." -ForegroundColor Yellow
            Stop-Job -Id $jobs.IngestionJobId -ErrorAction SilentlyContinue
            Remove-Job -Id $jobs.IngestionJobId -ErrorAction SilentlyContinue
        }
        
        Remove-Item $jobsFile -ErrorAction SilentlyContinue
    } catch {
        Write-Host "Could not read job file, attempting to stop by process name..." -ForegroundColor Yellow
    }
}

# Stop by process name as fallback
Write-Host "Stopping any remaining service processes..." -ForegroundColor Yellow

# Stop Python processes running main.py
Get-Process python -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*main.py*" } | Stop-Process -Force -ErrorAction SilentlyContinue

# Stop Rust ingestion service
Get-Process ingestion_service -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Stop any uvicorn processes
Get-Process uvicorn -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "✓ All services stopped" -ForegroundColor Green
