# AI-Assisted Email Response System - Comprehensive Testing Suite
# Phase 8: Local Deployment and Testing
#
# This script provides comprehensive testing capabilities including:
# - Unit tests for Rust and Python components
# - Integration tests across services
# - Code coverage measurement and reporting
# - Performance benchmarking
# - Security vulnerability scanning

param(
    [switch]$Unit,
    [switch]$Integration,
    [switch]$Coverage,
    [switch]$Performance,
    [switch]$Security,
    [switch]$All,
    [switch]$Rust,
    [switch]$Python,
    [switch]$Desktop,
    [string]$Component = "",
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ProjectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$script:TestResultsDir = Join-Path $ProjectRoot "test_results"
$script:CoverageDir = Join-Path $TestResultsDir "coverage"
$script:LogFile = Join-Path $TestResultsDir "test_execution.log"

# Test component definitions
$script:TestComponents = @{
    "ingestion_service" = @{
        Name = "Ingestion Service (Rust)"
        Type = "Rust"
        WorkingDir = Join-Path $ProjectRoot "ingestion_service"
        UnitTestCommand = "cargo test --lib"
        IntegrationTestCommand = "cargo test --test integration_tests"
        CoverageCommand = "cargo llvm-cov --html --output-dir"
        BenchmarkCommand = "cargo bench"
    }
    "rag_service" = @{
        Name = "RAG Service (Python)"
        Type = "Python"
        WorkingDir = Join-Path $ProjectRoot "rag_service"
        UnitTestCommand = "python -m pytest tests/ -v"
        IntegrationTestCommand = "python -m pytest test_rag_compliance.py test_compliance.py -v"
        CoverageCommand = "python -m pytest --cov=. --cov-report=html --cov-report=term"
        BenchmarkCommand = "python -m pytest --benchmark-only"
    }
    "desktop_app" = @{
        Name = "Desktop Application (Tauri)"
        Type = "Tauri"
        WorkingDir = Join-Path $ProjectRoot "desktop_app"
        UnitTestCommand = "npm test"
        IntegrationTestCommand = "npm run test:e2e"
        CoverageCommand = "npm run test:coverage"
        BenchmarkCommand = "npm run test:performance"
    }
}

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Ensure test results directory exists
    if (-not (Test-Path $script:TestResultsDir)) {
        New-Item -ItemType Directory -Path $script:TestResultsDir -Force | Out-Null
    }
    
    # Write to log file
    Add-Content -Path $script:LogFile -Value $logEntry
    
    # Write to console with colors
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        default { Write-Host $logEntry }
    }
}

function Test-Prerequisites {
    Write-TestLog "Checking test prerequisites..."
    
    $missing = @()
    
    # Check Rust toolchain
    try {
        $cargoVersion = cargo --version 2>$null
        if ($cargoVersion) {
            Write-TestLog "Rust/Cargo: $cargoVersion" "SUCCESS"
        } else {
            $missing += "Cargo (Rust toolchain)"
        }
    } catch {
        $missing += "Cargo (Rust toolchain)"
    }
    
    # Check Python
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion) {
            Write-TestLog "Python: $pythonVersion" "SUCCESS"
        } else {
            $missing += "Python"
        }
    } catch {
        $missing += "Python"
    }
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-TestLog "Node.js: $nodeVersion" "SUCCESS"
        } else {
            $missing += "Node.js"
        }
    } catch {
        $missing += "Node.js"
    }
    
    # Check for coverage tools
    if ($Coverage) {
        # Check cargo-llvm-cov
        try {
            cargo llvm-cov --version 2>$null | Out-Null
            Write-TestLog "cargo-llvm-cov available" "SUCCESS"
        } catch {
            Write-TestLog "Installing cargo-llvm-cov..." "INFO"
            cargo install cargo-llvm-cov
        }
        
        # Check pytest-cov
        try {
            python -c "import pytest_cov" 2>$null
            Write-TestLog "pytest-cov available" "SUCCESS"
        } catch {
            Write-TestLog "Installing pytest-cov..." "INFO"
            pip install pytest-cov
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-TestLog "Missing prerequisites: $($missing -join ', ')" "ERROR"
        return $false
    }
    
    return $true
}

function Invoke-RustTests {
    param([string]$ComponentName, [hashtable]$Config, [string]$TestType)
    
    Write-TestLog "Running $TestType tests for $($Config.Name)..."
    
    Push-Location $Config.WorkingDir
    
    try {
        $command = switch ($TestType) {
            "Unit" { $Config.UnitTestCommand }
            "Integration" { $Config.IntegrationTestCommand }
            "Coverage" { 
                $coverageDir = Join-Path $script:CoverageDir $ComponentName
                "$($Config.CoverageCommand) `"$coverageDir`""
            }
            "Benchmark" { $Config.BenchmarkCommand }
        }
        
        Write-TestLog "Executing: $command"
        
        if ($TestType -eq "Coverage") {
            # Ensure coverage directory exists
            $coverageDir = Join-Path $script:CoverageDir $ComponentName
            if (-not (Test-Path $coverageDir)) {
                New-Item -ItemType Directory -Path $coverageDir -Force | Out-Null
            }
        }
        
        $result = Invoke-Expression $command
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestLog "$TestType tests passed for $($Config.Name)" "SUCCESS"
            
            if ($TestType -eq "Coverage") {
                $coverageDir = Join-Path $script:CoverageDir $ComponentName
                if (Test-Path (Join-Path $coverageDir "index.html")) {
                    Write-TestLog "Coverage report generated: $coverageDir\index.html" "SUCCESS"
                }
            }
            
            return $true
        } else {
            Write-TestLog "$TestType tests failed for $($Config.Name)" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestLog "Error running $TestType tests for $($Config.Name): $_" "ERROR"
        return $false
    } finally {
        Pop-Location
    }
}

function Invoke-PythonTests {
    param([string]$ComponentName, [hashtable]$Config, [string]$TestType)
    
    Write-TestLog "Running $TestType tests for $($Config.Name)..."
    
    Push-Location $Config.WorkingDir
    
    try {
        $command = switch ($TestType) {
            "Unit" { $Config.UnitTestCommand }
            "Integration" { $Config.IntegrationTestCommand }
            "Coverage" { 
                $coverageDir = Join-Path $script:CoverageDir $ComponentName
                "$($Config.CoverageCommand) --cov-report=html:$coverageDir"
            }
            "Benchmark" { $Config.BenchmarkCommand }
        }
        
        Write-TestLog "Executing: $command"
        
        if ($TestType -eq "Coverage") {
            # Ensure coverage directory exists
            $coverageDir = Join-Path $script:CoverageDir $ComponentName
            if (-not (Test-Path $coverageDir)) {
                New-Item -ItemType Directory -Path $coverageDir -Force | Out-Null
            }
        }
        
        $result = Invoke-Expression $command
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestLog "$TestType tests passed for $($Config.Name)" "SUCCESS"
            
            if ($TestType -eq "Coverage") {
                $coverageDir = Join-Path $script:CoverageDir $ComponentName
                if (Test-Path (Join-Path $coverageDir "index.html")) {
                    Write-TestLog "Coverage report generated: $coverageDir\index.html" "SUCCESS"
                }
            }
            
            return $true
        } else {
            Write-TestLog "$TestType tests failed for $($Config.Name)" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestLog "Error running $TestType tests for $($Config.Name): $_" "ERROR"
        return $false
    } finally {
        Pop-Location
    }
}

function Invoke-TauriTests {
    param([string]$ComponentName, [hashtable]$Config, [string]$TestType)
    
    Write-TestLog "Running $TestType tests for $($Config.Name)..."
    
    Push-Location $Config.WorkingDir
    
    try {
        # Ensure node modules are installed
        if (-not (Test-Path "node_modules")) {
            Write-TestLog "Installing npm dependencies..."
            npm install
        }
        
        $command = switch ($TestType) {
            "Unit" { $Config.UnitTestCommand }
            "Integration" { $Config.IntegrationTestCommand }
            "Coverage" { $Config.CoverageCommand }
            "Benchmark" { $Config.BenchmarkCommand }
        }
        
        Write-TestLog "Executing: $command"
        
        $result = Invoke-Expression $command
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestLog "$TestType tests passed for $($Config.Name)" "SUCCESS"
            return $true
        } else {
            Write-TestLog "$TestType tests failed for $($Config.Name)" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestLog "Error running $TestType tests for $($Config.Name): $_" "ERROR"
        return $false
    } finally {
        Pop-Location
    }
}

function Invoke-ComponentTests {
    param([string]$ComponentName, [string]$TestType)
    
    $config = $script:TestComponents[$ComponentName]
    if (-not $config) {
        Write-TestLog "Unknown component: $ComponentName" "ERROR"
        return $false
    }
    
    if (-not (Test-Path $config.WorkingDir)) {
        Write-TestLog "Component directory not found: $($config.WorkingDir)" "ERROR"
        return $false
    }
    
    switch ($config.Type) {
        "Rust" { return Invoke-RustTests -ComponentName $ComponentName -Config $config -TestType $TestType }
        "Python" { return Invoke-PythonTests -ComponentName $ComponentName -Config $config -TestType $TestType }
        "Tauri" { return Invoke-TauriTests -ComponentName $ComponentName -Config $config -TestType $TestType }
        default {
            Write-TestLog "Unknown component type: $($config.Type)" "ERROR"
            return $false
        }
    }
}

function Invoke-SecurityScan {
    Write-TestLog "Running security vulnerability scans..."
    
    $results = @()
    
    # Rust security audit
    if (Test-Path (Join-Path $ProjectRoot "ingestion_service\Cargo.toml")) {
        Write-TestLog "Running Rust security audit..."
        Push-Location (Join-Path $ProjectRoot "ingestion_service")
        
        try {
            # Install cargo-audit if not available
            try {
                cargo audit --version 2>$null | Out-Null
            } catch {
                Write-TestLog "Installing cargo-audit..."
                cargo install cargo-audit
            }
            
            $auditResult = cargo audit --json 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "Rust security audit passed" "SUCCESS"
                $results += @{ Component = "Rust"; Status = "PASS"; Issues = 0 }
            } else {
                Write-TestLog "Rust security audit found vulnerabilities" "WARN"
                $results += @{ Component = "Rust"; Status = "WARN"; Issues = "Unknown" }
            }
        } catch {
            Write-TestLog "Error running Rust security audit: $_" "ERROR"
            $results += @{ Component = "Rust"; Status = "ERROR"; Issues = "Scan failed" }
        } finally {
            Pop-Location
        }
    }
    
    # Python security audit
    if (Test-Path (Join-Path $ProjectRoot "rag_service\requirements.txt")) {
        Write-TestLog "Running Python security audit..."
        Push-Location (Join-Path $ProjectRoot "rag_service")
        
        try {
            # Install safety if not available
            try {
                python -c "import safety" 2>$null
            } catch {
                Write-TestLog "Installing safety..."
                pip install safety
            }
            
            $safetyResult = python -m safety check --json 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "Python security audit passed" "SUCCESS"
                $results += @{ Component = "Python"; Status = "PASS"; Issues = 0 }
            } else {
                Write-TestLog "Python security audit found vulnerabilities" "WARN"
                $results += @{ Component = "Python"; Status = "WARN"; Issues = "Unknown" }
            }
        } catch {
            Write-TestLog "Error running Python security audit: $_" "ERROR"
            $results += @{ Component = "Python"; Status = "ERROR"; Issues = "Scan failed" }
        } finally {
            Pop-Location
        }
    }
    
    # Node.js security audit
    if (Test-Path (Join-Path $ProjectRoot "desktop_app\package.json")) {
        Write-TestLog "Running Node.js security audit..."
        Push-Location (Join-Path $ProjectRoot "desktop_app")
        
        try {
            $npmAuditResult = npm audit --json 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "Node.js security audit passed" "SUCCESS"
                $results += @{ Component = "Node.js"; Status = "PASS"; Issues = 0 }
            } else {
                Write-TestLog "Node.js security audit found vulnerabilities" "WARN"
                $results += @{ Component = "Node.js"; Status = "WARN"; Issues = "Unknown" }
            }
        } catch {
            Write-TestLog "Error running Node.js security audit: $_" "ERROR"
            $results += @{ Component = "Node.js"; Status = "ERROR"; Issues = "Scan failed" }
        } finally {
            Pop-Location
        }
    }
    
    # Generate security report
    Write-Host "`n=== Security Scan Results ===" -ForegroundColor Cyan
    foreach ($result in $results) {
        $color = switch ($result.Status) {
            "PASS" { "Green" }
            "WARN" { "Yellow" }
            "ERROR" { "Red" }
        }
        Write-Host "$($result.Component): $($result.Status) ($($result.Issues) issues)" -ForegroundColor $color
    }
    
    return $results
}

function Show-TestSummary {
    param([hashtable]$Results)
    
    Write-Host "`n=== Test Execution Summary ===" -ForegroundColor Cyan
    
    $totalTests = 0
    $passedTests = 0
    
    foreach ($component in $Results.Keys) {
        foreach ($testType in $Results[$component].Keys) {
            $totalTests++
            if ($Results[$component][$testType]) {
                $passedTests++
            }
            
            $status = if ($Results[$component][$testType]) { "✅ PASS" } else { "❌ FAIL" }
            $color = if ($Results[$component][$testType]) { "Green" } else { "Red" }
            
            Write-Host "$component - ${testType}: $status" -ForegroundColor $color
        }
    }
    
    Write-Host "`nOverall: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })
    
    if (Test-Path $script:CoverageDir) {
        Write-Host "`nCoverage reports available in: $script:CoverageDir" -ForegroundColor Cyan
    }
    
    if (Test-Path $script:LogFile) {
        Write-Host "Detailed logs available in: $script:LogFile" -ForegroundColor Cyan
    }
}

function Show-Help {
    Write-Host @"
AI-Assisted Email Response System - Testing Suite

USAGE:
    .\run_tests.ps1 [OPTIONS]

TEST TYPES:
    -Unit               Run unit tests
    -Integration        Run integration tests
    -Coverage           Generate code coverage reports
    -Performance        Run performance benchmarks
    -Security           Run security vulnerability scans
    -All                Run all test types

COMPONENT FILTERS:
    -Rust               Test only Rust components
    -Python             Test only Python components
    -Desktop            Test only desktop application
    -Component <name>   Test specific component (ingestion_service, rag_service, desktop_app)

OPTIONS:
    -Verbose            Enable verbose output
    -Help               Show this help message

EXAMPLES:
    .\run_tests.ps1 -All                           # Run all tests for all components
    .\run_tests.ps1 -Unit -Coverage                # Run unit tests with coverage
    .\run_tests.ps1 -Component rag_service -Unit   # Test only RAG service units
    .\run_tests.ps1 -Python -Integration           # Integration tests for Python components
    .\run_tests.ps1 -Security                      # Security vulnerability scan

OUTPUT:
    Test results: $script:TestResultsDir
    Coverage reports: $script:CoverageDir
    Execution logs: $script:LogFile

"@ -ForegroundColor White
}

# Main execution logic
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "=== AI Email Assistant - Testing Suite ===" -ForegroundColor Green
Write-TestLog "Test execution started with parameters: $($PSBoundParameters | ConvertTo-Json -Compress)"

# Check prerequisites
if (-not (Test-Prerequisites)) {
    Write-TestLog "Prerequisites check failed" "ERROR"
    exit 1
}

# Determine what to test
$componentsToTest = @()
if ($Component) {
    $componentsToTest = @($Component)
} elseif ($Rust) {
    $componentsToTest = @("ingestion_service")
} elseif ($Python) {
    $componentsToTest = @("rag_service")
} elseif ($Desktop) {
    $componentsToTest = @("desktop_app")
} else {
    $componentsToTest = $script:TestComponents.Keys
}

# Determine test types to run
$testTypes = @()
if ($All) {
    $testTypes = @("Unit", "Integration", "Coverage")
} else {
    if ($Unit) { $testTypes += "Unit" }
    if ($Integration) { $testTypes += "Integration" }
    if ($Coverage) { $testTypes += "Coverage" }
    if ($Performance) { $testTypes += "Benchmark" }
}

if ($testTypes.Count -eq 0 -and -not $Security) {
    Write-TestLog "No test types specified. Use -Help for usage information." "WARN"
    exit 1
}

# Execute tests
$results = @{}

foreach ($component in $componentsToTest) {
    $results[$component] = @{}
    
    foreach ($testType in $testTypes) {
        $results[$component][$testType] = Invoke-ComponentTests -ComponentName $component -TestType $testType
    }
}

# Run security scan if requested
if ($Security) {
    Invoke-SecurityScan
}

# Show summary
Show-TestSummary -Results $results

Write-TestLog "Test execution completed"
