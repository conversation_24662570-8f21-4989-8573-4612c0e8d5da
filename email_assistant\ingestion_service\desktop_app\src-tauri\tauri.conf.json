{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "desktop_app", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "here", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "desktop_app", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}