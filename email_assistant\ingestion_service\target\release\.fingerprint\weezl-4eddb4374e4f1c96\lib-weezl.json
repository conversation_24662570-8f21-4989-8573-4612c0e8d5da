{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2040997289075261528, "path": 5154594156542410123, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\weezl-4eddb4374e4f1c96\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}