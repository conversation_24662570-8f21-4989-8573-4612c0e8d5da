// Enhanced mbox Format with Threading and Weighting Metadata
// Extends standard mbox format with custom X-headers for conversation threading

use std::io::{self, Write};
use std::fs::File;
use chrono::Utc;
use crate::email_threading::{ThreadedEmail, EmailType};

/// Enhanced mbox writer with threading metadata
pub struct EnhancedMboxWriter {
    file: File,
    email_count: u32,
}

impl EnhancedMboxWriter {
    pub fn new(file_path: &str) -> io::Result<Self> {
        let file = File::create(file_path)?;
        Ok(Self {
            file,
            email_count: 0,
        })
    }

    /// Write a threaded email to the enhanced mbox format
    pub fn write_email(&mut self, email: &ThreadedEmail) -> io::Result<()> {
        // Write mbox separator line
        let from_line = format!(
            "From {} {}\n",
            email.from.as_deref().unwrap_or("<EMAIL>"),
            email.sent_date
                .unwrap_or_else(|| Utc::now())
                .format("%a %b %d %H:%M:%S %Y")
        );
        self.file.write_all(from_line.as_bytes())?;

        // Write standard email headers
        self.write_standard_headers(email)?;
        
        // Write enhanced threading headers
        self.write_threading_headers(email)?;
        
        // Write weighting and classification headers
        self.write_weighting_headers(email)?;
        
        // Write processing metadata headers
        self.write_processing_headers(email)?;
        
        // Write body separator
        self.file.write_all(b"\n")?;
        
        // Write email body
        if let Some(body) = &email.cleaned_plain_text_body {
            // Escape any lines that start with "From " to prevent mbox corruption
            for line in body.lines() {
                if line.starts_with("From ") {
                    self.file.write_all(b">")?;
                }
                self.file.write_all(line.as_bytes())?;
                self.file.write_all(b"\n")?;
            }
        }
        
        // Write final separator
        self.file.write_all(b"\n")?;
        
        self.email_count += 1;
        Ok(())
    }

    /// Write standard RFC 5322 headers
    fn write_standard_headers(&mut self, email: &ThreadedEmail) -> io::Result<()> {
        // Message-ID
        if let Some(message_id) = &email.message_id {
            writeln!(self.file, "Message-ID: {}", message_id)?;
        }
        
        // Date
        if let Some(date) = &email.sent_date {
            writeln!(self.file, "Date: {}", date.format("%a, %d %b %Y %H:%M:%S %z"))?;
        }
        
        // From
        if let Some(from) = &email.from {
            writeln!(self.file, "From: {}", from)?;
        }
        
        // To
        if !email.to.is_empty() {
            writeln!(self.file, "To: {}", email.to.join(", "))?;
        }
        
        // Subject
        if let Some(subject) = &email.subject {
            writeln!(self.file, "Subject: {}", subject)?;
        }
        
        // In-Reply-To
        if !email.in_reply_to.is_empty() {
            writeln!(self.file, "In-Reply-To: {}", email.in_reply_to.join(" "))?;
        }
        
        // References
        if !email.references.is_empty() {
            writeln!(self.file, "References: {}", email.references.join(" "))?;
        }
        
        Ok(())
    }

    /// Write custom threading headers
    fn write_threading_headers(&mut self, email: &ThreadedEmail) -> io::Result<()> {
        // Thread ID
        if let Some(thread_id) = &email.thread_id {
            writeln!(self.file, "X-Thread-ID: {}", thread_id)?;
        }
        
        // Conversation ID
        if let Some(conversation_id) = &email.conversation_id {
            writeln!(self.file, "X-Conversation-ID: {}", conversation_id)?;
        }
        
        // Thread position
        if let Some(position) = email.thread_position {
            writeln!(self.file, "X-Thread-Position: {}", position)?;
        }
        
        // Normalized subject for threading
        if let Some(normalized_subject) = &email.normalized_subject {
            writeln!(self.file, "X-Normalized-Subject: {}", normalized_subject)?;
        }
        
        Ok(())
    }

    /// Write weighting and classification headers
    fn write_weighting_headers(&mut self, email: &ThreadedEmail) -> io::Result<()> {
        // Email type classification
        let email_type_str = match email.email_type {
            EmailType::Received => "received",
            EmailType::Sent => "sent",
            EmailType::Draft => "draft",
            EmailType::Unknown => "unknown",
        };
        writeln!(self.file, "X-Email-Type: {}", email_type_str)?;
        
        // Weight for AI processing
        writeln!(self.file, "X-Email-Weight: {:.2}", email.weight)?;
        
        // Original folder path
        if let Some(folder_path) = &email.folder_path {
            writeln!(self.file, "X-Original-Folder: {}", folder_path)?;
        }
        
        Ok(())
    }

    /// Write processing metadata headers
    fn write_processing_headers(&mut self, email: &ThreadedEmail) -> io::Result<()> {
        // Content hash for duplicate detection
        if let Some(content_hash) = &email.content_hash {
            writeln!(self.file, "X-Content-Hash: {}", content_hash)?;
        }
        
        // Duplicate detection results
        if email.is_duplicate {
            writeln!(self.file, "X-Is-Duplicate: true")?;
            if let Some(duplicate_of) = &email.duplicate_of {
                writeln!(self.file, "X-Duplicate-Of: {}", duplicate_of)?;
            }
        }
        
        // Processing notes
        if !email.processing_notes.is_empty() {
            for note in &email.processing_notes {
                writeln!(self.file, "X-Processing-Note: {}", note)?;
            }
        }
        
        // Processing timestamp
        writeln!(self.file, "X-Processed-At: {}", Utc::now().to_rfc3339())?;

        // Case cataloging headers
        if let Some(case_id) = &email.case_id {
            writeln!(self.file, "X-Case-ID: {}", case_id)?;
        }
        if let Some(case_subject) = &email.case_subject {
            writeln!(self.file, "X-Case-Subject: {}", case_subject)?;
        }
        if !email.case_participants.is_empty() {
            writeln!(self.file, "X-Case-Participants: {}", email.case_participants.join(", "))?;
        }
        
        // Internal ID for tracking
        writeln!(self.file, "X-Internal-ID: {}", email.id)?;
        
        Ok(())
    }

    /// Get the number of emails written
    pub fn email_count(&self) -> u32 {
        self.email_count
    }

    /// Flush and close the file
    pub fn close(mut self) -> io::Result<()> {
        self.file.flush()?;
        Ok(())
    }
}

/// Enhanced mbox reader that can parse threading metadata
pub struct EnhancedMboxReader;

impl EnhancedMboxReader {
    /// Parse threading metadata from email headers
    pub fn parse_threading_metadata(headers: &[(String, String)]) -> ThreadingMetadata {
        let mut metadata = ThreadingMetadata::default();
        
        for (name, value) in headers {
            match name.to_lowercase().as_str() {
                "x-thread-id" => metadata.thread_id = Some(value.clone()),
                "x-conversation-id" => metadata.conversation_id = Some(value.clone()),
                "x-thread-position" => {
                    if let Ok(pos) = value.parse::<u32>() {
                        metadata.thread_position = Some(pos);
                    }
                }
                "x-normalized-subject" => metadata.normalized_subject = Some(value.clone()),
                "x-email-type" => {
                    metadata.email_type = match value.as_str() {
                        "sent" => EmailType::Sent,
                        "received" => EmailType::Received,
                        "draft" => EmailType::Draft,
                        _ => EmailType::Unknown,
                    };
                }
                "x-email-weight" => {
                    if let Ok(weight) = value.parse::<f32>() {
                        metadata.weight = weight;
                    }
                }
                "x-original-folder" => metadata.folder_path = Some(value.clone()),
                "x-content-hash" => metadata.content_hash = Some(value.clone()),
                "x-is-duplicate" => metadata.is_duplicate = value == "true",
                "x-duplicate-of" => {
                    if let Ok(uuid) = value.parse() {
                        metadata.duplicate_of = Some(uuid);
                    }
                }
                "x-processing-note" => metadata.processing_notes.push(value.clone()),
                "x-internal-id" => {
                    if let Ok(uuid) = value.parse() {
                        metadata.internal_id = Some(uuid);
                    }
                }
                _ => {} // Ignore unknown headers
            }
        }
        
        metadata
    }
}

/// Parsed threading metadata from enhanced mbox headers
#[derive(Debug, Default)]
pub struct ThreadingMetadata {
    pub thread_id: Option<String>,
    pub conversation_id: Option<String>,
    pub thread_position: Option<u32>,
    pub normalized_subject: Option<String>,
    pub email_type: EmailType,
    pub weight: f32,
    pub folder_path: Option<String>,
    pub content_hash: Option<String>,
    pub is_duplicate: bool,
    pub duplicate_of: Option<uuid::Uuid>,
    pub processing_notes: Vec<String>,
    pub internal_id: Option<uuid::Uuid>,
}

impl Default for EmailType {
    fn default() -> Self {
        EmailType::Unknown
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use uuid::Uuid;

    #[test]
    fn test_enhanced_mbox_writer() -> io::Result<()> {
        let temp_file = NamedTempFile::new()?;
        let mut writer = EnhancedMboxWriter::new(temp_file.path().to_str().unwrap())?;
        
        let email = ThreadedEmail {
            id: Uuid::new_v4(),
            subject: Some("Test Subject".to_string()),
            from: Some("<EMAIL>".to_string()),
            to: vec!["<EMAIL>".to_string()],
            sent_date: Some(Utc::now()),
            plain_text_body_raw: None,
            html_body_raw: None,
            cleaned_plain_text_body: Some("Test email body".to_string()),
            message_id: Some("<<EMAIL>>".to_string()),
            in_reply_to: vec![],
            references: vec![],
            thread_id: Some("thread-123".to_string()),
            conversation_id: Some("conv-123".to_string()),
            thread_position: Some(1),
            email_type: EmailType::Received,
            weight: 0.34,
            folder_path: Some("INBOX".to_string()),
            content_hash: Some("abc123".to_string()),
            normalized_subject: Some("test subject".to_string()),
            is_duplicate: false,
            duplicate_of: None,
            processing_notes: vec!["Processed successfully".to_string()],

            // Case cataloging fields
            case_id: None,
            case_subject: None,
            case_participants: vec![],
        };
        
        writer.write_email(&email)?;
        assert_eq!(writer.email_count(), 1);
        
        writer.close()?;
        Ok(())
    }

    #[test]
    fn test_parse_threading_metadata() {
        let headers = vec![
            ("X-Thread-ID".to_string(), "thread-123".to_string()),
            ("X-Email-Type".to_string(), "sent".to_string()),
            ("X-Email-Weight".to_string(), "0.66".to_string()),
        ];
        
        let metadata = EnhancedMboxReader::parse_threading_metadata(&headers);
        assert_eq!(metadata.thread_id, Some("thread-123".to_string()));
        assert_eq!(metadata.email_type, EmailType::Sent);
        assert_eq!(metadata.weight, 0.66);
    }
}
