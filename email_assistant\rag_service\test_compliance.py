"""
Test script for compliance features (logging and pseudonymisation)

This script tests the compliance logging and pseudonymisation features
to ensure they work correctly with the RAG pipeline.
"""

import asyncio
import os
import json
import tempfile
from pathlib import Path
import sys

# Add the rag_service directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from compliance_logger import ComplianceLogger, get_compliance_logger
from pseudonymizer import P<PERSON>udonymizer, PseudonymisationConfig, PseudonymisationLevel


def test_compliance_logger():
    """Test the compliance logger functionality"""
    print("Testing Compliance Logger...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize logger with test directory
        logger = ComplianceLogger(
            log_dir=temp_dir,
            log_level="INFO",
            max_file_size=1024 * 1024,  # 1MB for testing
            backup_count=5
        )
        
        # Test transaction ID generation
        transaction_id = logger.generate_transaction_id()
        print(f"✓ Generated transaction ID: {transaction_id}")
        
        # Test request logging
        request_data = {
            "subject": "Test Legal Inquiry",
            "sender": "<EMAIL>",
            "content": "I need legal advice regarding contract terms."
        }
        logger.log_draft_request(transaction_id, request_data)
        print("✓ Logged draft request")
        
        # Test context retrieval logging
        query_embedding_info = {"dimension": 384, "model": "all-MiniLM-L6-v2"}
        retrieved_emails = [
            {
                "id": "email_1",
                "similarity_score": 0.85,
                "subject": "Contract Review",
                "content": "Previous contract advice..."
            }
        ]
        search_metadata = {"similarity_threshold": 0.7, "max_context_emails": 5}
        logger.log_context_retrieval(transaction_id, query_embedding_info, retrieved_emails, search_metadata)
        print("✓ Logged context retrieval")
        
        # Test prompt composition logging
        context_summary = {"email_count": 1, "total_context_length": 500}
        logger.log_prompt_composition(transaction_id, "legal_advisor_rag_v1", context_summary, 1200)
        print("✓ Logged prompt composition")
        
        # Test LLM generation logging
        llm_metadata = {"model": "llama3", "base_url": "http://localhost:11434", "generation_time_ms": 2500}
        generation_result = {"draft": "Thank you for your inquiry...", "success": True, "error": None}
        logger.log_llm_generation(transaction_id, llm_metadata, generation_result)
        print("✓ Logged LLM generation")
        
        # Test final response logging
        final_response = {
            "draft": "Thank you for your inquiry. Based on similar cases...",
            "context_emails_count": 1,
            "similar_emails": retrieved_emails,
            "metadata": {"model": "llama3"}
        }
        processing_metadata = {"total_processing_time_ms": 3000, "pseudonymisation_applied": False}
        logger.log_draft_response(transaction_id, final_response, processing_metadata)
        print("✓ Logged draft response")
        
        # Test error logging
        logger.log_error(transaction_id, "test_error", "This is a test error", {"context": "testing"})
        print("✓ Logged error")
        
        # Close the logger to release file handles
        logger.close()

        # Check if log file was created
        log_file = Path(temp_dir) / "compliance_audit.log"
        if log_file.exists():
            print(f"✓ Log file created: {log_file}")

            # Read and validate log entries
            with open(log_file, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
                print(f"✓ Log contains {len(log_lines)} entries")

                # Validate JSON format
                for i, line in enumerate(log_lines):
                    try:
                        log_entry = json.loads(line.strip())
                        assert "transaction_id" in log_entry
                        assert "timestamp" in log_entry
                        assert "event_type" in log_entry
                        print(f"✓ Log entry {i+1} is valid JSON with required fields")
                    except (json.JSONDecodeError, AssertionError) as e:
                        print(f"✗ Log entry {i+1} is invalid: {e}")
                        return False
        else:
            print("✗ Log file was not created")
            return False
    
    print("✓ Compliance Logger tests passed!\n")
    return True


def test_pseudonymizer():
    """Test the pseudonymiser functionality"""
    print("Testing Pseudonymizer...")
    
    # Test with disabled pseudonymisation
    config = PseudonymisationConfig()
    config.enabled = False
    pseudonymizer = Pseudonymizer(config)
    
    test_text = "<NAME_EMAIL> or call ************ for more information."
    result = pseudonymizer.pseudonymize_text(test_text)
    assert result == test_text, "Disabled pseudonymisation should return original text"
    print("✓ Disabled pseudonymisation works correctly")
    
    # Test with basic pseudonymisation
    config = PseudonymisationConfig()
    config.enabled = True
    config.level = PseudonymisationLevel.BASIC
    config.rules = config._initialize_rules()  # Re-initialize rules with new level
    pseudonymizer = Pseudonymizer(config)

    result = pseudonymizer.pseudonymize_text(test_text)
    print(f"  Original: {test_text}")
    print(f"  Pseudonymised: {result}")
    print(f"  Rules enabled: {[r.name for r in pseudonymizer.config.rules if r.enabled]}")

    if "<EMAIL>" in result:
        print("  Debug: Email was not pseudonymised - checking pattern matching...")
        import re
        pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b'
        matches = re.findall(pattern, test_text)
        print(f"  Debug: Pattern matches: {matches}")

    assert "<EMAIL>" not in result, "Email should be pseudonymised"
    assert "[EMAIL_" in result, "Email should be replaced with placeholder"
    print("✓ Basic pseudonymisation works correctly")
    
    # Test with moderate pseudonymisation
    config.level = PseudonymisationLevel.MODERATE
    config.rules = config._initialize_rules()  # Re-initialize rules with new level
    pseudonymizer = Pseudonymizer(config)
    
    test_text_with_names = "John <NAME_EMAIL> about the contract. Call ************."
    result = pseudonymizer.pseudonymize_text(test_text_with_names)
    assert "<EMAIL>" not in result, "Email should be pseudonymised"
    assert "************" not in result, "Phone should be pseudonymised"
    assert "John Smith" not in result, "Name should be pseudonymised"
    print("✓ Moderate pseudonymisation works correctly")
    print(f"  Original: {test_text_with_names}")
    print(f"  Pseudonymised: {result}")
    
    # Test deterministic pseudonymisation
    config.use_deterministic = True
    config.rules = config._initialize_rules()  # Re-initialize rules
    pseudonymizer1 = Pseudonymizer(config)
    pseudonymizer2 = Pseudonymizer(config)
    
    # Set same session key
    pseudonymizer1.session_key = "test_key"
    pseudonymizer2.session_key = "test_key"
    
    result1 = pseudonymizer1.pseudonymize_text("<EMAIL>")
    result2 = pseudonymizer2.pseudonymize_text("<EMAIL>")
    assert result1 == result2, "Deterministic pseudonymisation should produce same results"
    print("✓ Deterministic pseudonymisation works correctly")
    
    # Test replacement statistics
    stats = pseudonymizer.get_replacement_stats()
    assert isinstance(stats, dict), "Stats should be a dictionary"
    assert "total_replacements" in stats, "Stats should include replacement count"
    print(f"✓ Replacement statistics: {stats}")
    
    print("✓ Pseudonymizer tests passed!\n")
    return True


def test_integration():
    """Test integration between compliance logger and pseudonymiser"""
    print("Testing Integration...")
    
    # Test the global instances
    compliance_logger = get_compliance_logger()
    assert compliance_logger is not None, "Global compliance logger should be available"
    print("✓ Global compliance logger available")
    
    # Test transaction ID generation
    transaction_id = compliance_logger.generate_transaction_id()
    assert len(transaction_id) > 0, "Transaction ID should not be empty"
    print(f"✓ Transaction ID generated: {transaction_id}")
    
    # Test email content pseudonymisation
    from pseudonymizer import pseudonymize_email_content
    
    subject = "Contract Review for John Smith"
    sender = "<EMAIL>"
    content = "Please review the contract and contact me at ************."
    
    pseudo_subject, pseudo_sender, pseudo_content = pseudonymize_email_content(subject, sender, content)
    
    # With pseudonymisation disabled by default, should return original
    assert pseudo_subject == subject
    assert pseudo_sender == sender
    assert pseudo_content == content
    print("✓ Email content pseudonymisation works (disabled by default)")
    
    print("✓ Integration tests passed!\n")
    return True


async def test_environment_configuration():
    """Test environment configuration loading"""
    print("Testing Environment Configuration...")
    
    # Test default configuration
    original_env = os.environ.copy()
    
    try:
        # Clear relevant environment variables
        for key in list(os.environ.keys()):
            if key.startswith(("COMPLIANCE_", "PSEUDONYMISATION_")):
                del os.environ[key]
        
        # Test defaults
        compliance_logger = ComplianceLogger()
        assert compliance_logger.log_dir == Path("logs")
        assert compliance_logger.log_level == 20  # INFO level
        print("✓ Default configuration loaded correctly")
        
        # Test custom configuration
        os.environ["COMPLIANCE_LOG_DIR"] = "custom_logs"
        os.environ["COMPLIANCE_LOG_LEVEL"] = "DEBUG"
        os.environ["PSEUDONYMISATION_ENABLED"] = "true"
        os.environ["PSEUDONYMISATION_LEVEL"] = "moderate"
        
        # Reload configuration
        from pseudonymizer import PseudonymisationConfig
        config = PseudonymisationConfig()
        assert config.enabled == True
        assert config.level == PseudonymisationLevel.MODERATE
        print("✓ Custom configuration loaded correctly")
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)
    
    print("✓ Environment configuration tests passed!\n")
    return True


async def main():
    """Run all compliance tests"""
    print("=" * 60)
    print("AI-Assisted Email Response System - Compliance Tests")
    print("=" * 60)
    print()
    
    tests = [
        ("Compliance Logger", test_compliance_logger),
        ("Pseudonymizer", test_pseudonymizer),
        ("Integration", test_integration),
        ("Environment Configuration", test_environment_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name} tests...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✓ {test_name} tests PASSED")
            else:
                print(f"✗ {test_name} tests FAILED")
        except Exception as e:
            print(f"✗ {test_name} tests FAILED with exception: {e}")
        
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All compliance tests passed! The system is ready for Phase 7.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())


# Additional pytest-compatible test functions for unit testing
import pytest

class TestComplianceLoggerUnit:
    """Unit tests for ComplianceLogger using pytest"""

    def test_transaction_id_generation(self):
        """Test transaction ID generation"""
        logger = ComplianceLogger(log_dir="test_logs")

        # Generate multiple transaction IDs
        id1 = logger.generate_transaction_id()
        id2 = logger.generate_transaction_id()

        assert id1 != id2
        assert len(id1) > 0
        assert len(id2) > 0
        assert isinstance(id1, str)
        assert isinstance(id2, str)

        logger.close()

    def test_json_formatter(self):
        """Test JSON formatter functionality"""
        formatter = ComplianceJSONFormatter()

        # Create a mock log record
        import logging
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg='{"test": "message"}',
            args=(),
            exc_info=None
        )

        result = formatter.format(record)

        # Should be valid JSON
        import json
        parsed = json.loads(result)
        assert "test" in parsed
        assert parsed["test"] == "message"
        assert "log_level" in parsed
        assert parsed["log_level"] == "INFO"


class TestPseudonymizerUnit:
    """Unit tests for Pseudonymizer using pytest"""

    def test_email_pattern_matching(self):
        """Test email pattern detection and replacement"""
        config = PseudonymisationConfig()
        config.enabled = True
        config.level = PseudonymisationLevel.BASIC
        config.rules = config._initialize_rules()

        pseudonymizer = Pseudonymizer(config)

        test_cases = [
            "Contact <EMAIL> for more info",
            "Email <NAME_EMAIL>",
            "<NAME_EMAIL>"
        ]

        for test_text in test_cases:
            result = pseudonymizer.pseudonymize_text(test_text)

            # Should not contain original email
            assert "@" not in result or "[EMAIL_" in result

            # Should contain placeholder
            if "@" in test_text:
                assert "[EMAIL_" in result

    def test_phone_pattern_matching(self):
        """Test phone number pattern detection"""
        config = PseudonymisationConfig()
        config.enabled = True
        config.level = PseudonymisationLevel.MODERATE
        config.rules = config._initialize_rules()

        pseudonymizer = Pseudonymizer(config)

        test_cases = [
            "Call me at ************",
            "Phone: (*************",
            "Contact: ************"
        ]

        for test_text in test_cases:
            result = pseudonymizer.pseudonymize_text(test_text)

            # Should contain phone placeholder
            assert "[PHONE_" in result

    def test_deterministic_pseudonymisation(self):
        """Test deterministic pseudonymisation consistency"""
        config = PseudonymisationConfig()
        config.enabled = True
        config.level = PseudonymisationLevel.BASIC
        config.use_deterministic = True
        config.rules = config._initialize_rules()

        pseudonymizer1 = Pseudonymizer(config)
        pseudonymizer2 = Pseudonymizer(config)

        # Set same session key
        pseudonymizer1.session_key = "test_key"
        pseudonymizer2.session_key = "test_key"

        test_text = "Contact <EMAIL>"

        result1 = pseudonymizer1.pseudonymize_text(test_text)
        result2 = pseudonymizer2.pseudonymize_text(test_text)

        assert result1 == result2

    def test_disabled_pseudonymisation(self):
        """Test that disabled pseudonymisation returns original text"""
        config = PseudonymisationConfig()
        config.enabled = False

        pseudonymizer = Pseudonymizer(config)

        test_text = "Contact <EMAIL> or call ************"
        result = pseudonymizer.pseudonymize_text(test_text)

        assert result == test_text

    def test_replacement_statistics(self):
        """Test replacement statistics tracking"""
        config = PseudonymisationConfig()
        config.enabled = True
        config.level = PseudonymisationLevel.BASIC
        config.rules = config._initialize_rules()

        pseudonymizer = Pseudonymizer(config)

        test_text = "Email john@example.<NAME_EMAIL>"
        pseudonymizer.pseudonymize_text(test_text)

        stats = pseudonymizer.get_replacement_stats()

        assert isinstance(stats, dict)
        assert "total_replacements" in stats
        assert stats["total_replacements"] >= 0
        assert "config_level" in stats
        assert stats["config_level"] == "basic"
