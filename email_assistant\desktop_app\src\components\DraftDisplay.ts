// AI-Assisted Email Response System - Draft Display Component

import type { DraftResponse } from '../types';

// Note: Clipboard functionality will be added when the plugin is properly configured

export class DraftDisplay {
  private container: HTMLElement;
  private draftContent: string = '';
  private loading: boolean = false;

  constructor(container: HTMLElement) {
    this.container = container;
    this.render();
  }

  private render() {
    this.container.innerHTML = `
      <div class="draft-display-container">
        <div class="draft-display-header">
          <h2>Generated Draft Reply</h2>
          <div class="draft-display-actions">
            <button id="copy-draft-btn" class="btn btn-secondary" style="display: none;">
              <span class="icon">📋</span>
              Copy to Clipboard
            </button>
            <button id="clear-draft-btn" class="btn btn-outline" style="display: none;">
              <span class="icon">🗑️</span>
              Clear
            </button>
          </div>
        </div>
        <div class="draft-display-content">
          <div id="draft-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Generating draft reply...</p>
          </div>
          <div id="draft-error" class="error-state" style="display: none;"></div>
          <div id="draft-content" class="draft-content-area">
            <div class="empty-state">
              <span class="icon">✨</span>
              <h3>No draft generated</h3>
              <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add event listeners
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLButtonElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLButtonElement;

    copyBtn?.addEventListener('click', () => this.copyToClipboard());
    clearBtn?.addEventListener('click', () => this.clearDraft());
  }

  public setLoading(loading: boolean) {
    this.loading = loading;
    this.renderLoadingState();
  }

  private renderLoadingState() {
    const loadingEl = this.container.querySelector('#draft-loading') as HTMLElement;
    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;
    
    if (this.loading) {
      loadingEl.style.display = 'flex';
      contentEl.style.display = 'none';
      copyBtn.style.display = 'none';
      clearBtn.style.display = 'none';
    } else {
      loadingEl.style.display = 'none';
      contentEl.style.display = 'block';
    }
  }

  public renderError(message: string) {
    const errorEl = this.container.querySelector('#draft-error') as HTMLElement;
    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;
    
    errorEl.innerHTML = `
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${message}</p>
        <button class="btn btn-primary" onclick="this.retryGeneration()">Retry</button>
      </div>
    `;
    
    errorEl.style.display = 'block';
    contentEl.style.display = 'none';
    copyBtn.style.display = 'none';
    clearBtn.style.display = 'none';
  }

  public displayDraftText(content: string, metadata?: {
    contextEmailsCount?: number;
    similarEmails?: any[];
    metadata?: any;
  }) {
    this.draftContent = content;

    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const errorEl = this.container.querySelector('#draft-error') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;

    errorEl.style.display = 'none';

    const contextInfo = metadata?.contextEmailsCount
      ? `Based on ${metadata.contextEmailsCount} similar email${metadata.contextEmailsCount !== 1 ? 's' : ''}`
      : 'Generated using AI';

    contentEl.innerHTML = `
      <div class="draft-text-container">
        <div class="draft-metadata">
          <span class="draft-timestamp">Generated: ${new Date().toLocaleString()}</span>
          <span class="draft-length">${content.length} characters</span>
          <span class="draft-context">${contextInfo}</span>
        </div>
        <div class="draft-text-area">
          <textarea
            class="draft-textarea"
            readonly
            placeholder="Generated draft will appear here..."
          >${this.escapeHtml(content)}</textarea>
        </div>
        <div class="draft-actions-bottom">
          <button class="btn btn-outline" onclick="this.editDraft()">
            <span class="icon">✏️</span>
            Edit Draft
          </button>
          <button class="btn btn-secondary" onclick="this.regenerateDraft()">
            <span class="icon">🔄</span>
            Regenerate
          </button>
        </div>
      </div>
    `;

    contentEl.style.display = 'block';
    copyBtn.style.display = 'inline-flex';
    clearBtn.style.display = 'inline-flex';
  }

  // New method to display DraftResponse with weighting information
  public displayDraft(draftResponse: DraftResponse) {
    this.draftContent = draftResponse.draft;

    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const errorEl = this.container.querySelector('#draft-error') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;

    errorEl.style.display = 'none';

    // Extract weighting information from metadata
    const contextInfo = draftResponse.context_emails_count
      ? `Based on ${draftResponse.context_emails_count} similar email${draftResponse.context_emails_count !== 1 ? 's' : ''}`
      : 'Generated using AI';

    // Extract email weighting information if available
    let weightingInfo = '';
    if (draftResponse.metadata && draftResponse.metadata.email_weights) {
      const weights = draftResponse.metadata.email_weights;
      weightingInfo = `<div class="weighting-info">
        <span class="weighting-label">Email Weighting Applied:</span>
        <span class="weighting-details">Sent: ${Math.round((weights.sent || 0.66) * 100)}%, Inbox: ${Math.round((weights.inbox || 0.34) * 100)}%</span>
      </div>`;
    }

    // Show similar emails information if available
    let similarEmailsInfo = '';
    if (draftResponse.similar_emails && draftResponse.similar_emails.length > 0) {
      similarEmailsInfo = `<div class="similar-emails-info">
        <span class="similar-emails-label">Similar emails found:</span>
        <span class="similar-emails-count">${draftResponse.similar_emails.length}</span>
      </div>`;
    }

    contentEl.innerHTML = `
      <div class="draft-text-container">
        <div class="draft-metadata">
          <span class="draft-timestamp">Generated: ${new Date().toLocaleString()}</span>
          <span class="draft-length">${draftResponse.draft.length} characters</span>
          <span class="draft-context">${contextInfo}</span>
          ${weightingInfo}
          ${similarEmailsInfo}
        </div>
        <div class="draft-text-area">
          <textarea
            class="draft-textarea"
            readonly
            placeholder="Generated draft will appear here..."
          >${this.escapeHtml(draftResponse.draft)}</textarea>
        </div>
        <div class="draft-actions-bottom">
          <button class="btn btn-outline" onclick="this.editDraft()">
            <span class="icon">✏️</span>
            Edit Draft
          </button>
          <button class="btn btn-secondary" onclick="this.regenerateDraft()">
            <span class="icon">🔄</span>
            Regenerate
          </button>
        </div>
      </div>
    `;

    contentEl.style.display = 'block';
    copyBtn.style.display = 'inline-flex';
    clearBtn.style.display = 'inline-flex';
  }

  public displayError(message: string) {
    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const errorEl = this.container.querySelector('#draft-error') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;

    errorEl.innerHTML = `
      <div class="error-message">
        <span class="icon">⚠️</span>
        <h3>Draft Generation Failed</h3>
        <p>${this.escapeHtml(message)}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `;

    errorEl.style.display = 'block';
    contentEl.style.display = 'none';
    copyBtn.style.display = 'none';
    clearBtn.style.display = 'none';
  }

  public displayPlaceholder() {
    this.draftContent = '';
    
    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const errorEl = this.container.querySelector('#draft-error') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;
    
    errorEl.style.display = 'none';
    
    // For now, display a placeholder since RAG service is not implemented yet
    contentEl.innerHTML = `
      <div class="placeholder-state">
        <span class="icon">🚧</span>
        <h3>Draft Generation Coming Soon</h3>
        <p>The RAG service for AI-powered draft generation is currently being developed.</p>
        <p>For now, you can view email details and prepare for draft generation functionality.</p>
        <div class="placeholder-draft">
          <h4>Sample Draft Preview:</h4>
          <div class="sample-draft">
            <p>Dear [Sender Name],</p>
            <p>Thank you for your email regarding [Subject]. I have reviewed your message and would like to respond as follows:</p>
            <p>[AI-generated response content will appear here]</p>
            <p>Please let me know if you need any additional information.</p>
            <p>Best regards,<br>[Your Name]</p>
          </div>
        </div>
      </div>
    `;

    contentEl.style.display = 'block';
    copyBtn.style.display = 'none';
    clearBtn.style.display = 'none';
  }

  private async copyToClipboard() {
    if (!this.draftContent) return;

    try {
      // Fallback to browser clipboard API for now
      await navigator.clipboard.writeText(this.draftContent);
      this.showCopySuccess();
    } catch (error) {
      this.showCopyError(`Failed to copy to clipboard: ${error}`);
    }
  }

  private showCopySuccess() {
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLButtonElement;
    const originalText = copyBtn.innerHTML;
    
    copyBtn.innerHTML = '<span class="icon">✅</span> Copied!';
    copyBtn.disabled = true;
    
    setTimeout(() => {
      copyBtn.innerHTML = originalText;
      copyBtn.disabled = false;
    }, 2000);
  }

  private showCopyError(message: string) {
    // Could show a toast notification or inline error
    console.error(message);
    alert(message); // Simple fallback for now
  }

  private clearDraft() {
    this.draftContent = '';
    
    const contentEl = this.container.querySelector('#draft-content') as HTMLElement;
    const copyBtn = this.container.querySelector('#copy-draft-btn') as HTMLElement;
    const clearBtn = this.container.querySelector('#clear-draft-btn') as HTMLElement;
    
    contentEl.innerHTML = `
      <div class="empty-state">
        <span class="icon">✨</span>
        <h3>No draft generated</h3>
        <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
      </div>
    `;
    
    copyBtn.style.display = 'none';
    clearBtn.style.display = 'none';
  }

  public editDraft() {
    const textarea = this.container.querySelector('.draft-textarea') as HTMLTextAreaElement;
    if (textarea) {
      textarea.readOnly = false;
      textarea.focus();

      // Update the draft content when user edits
      textarea.addEventListener('input', () => {
        this.draftContent = textarea.value;
      });
    }
  }

  public regenerateDraft() {
    // This would trigger a new draft generation
    // For now, just show the placeholder
    this.displayPlaceholder();
  }

  public retryGeneration() {
    // This would retry the last failed generation
    this.displayPlaceholder();
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  public getDraftContent(): string {
    return this.draftContent;
  }

  public hasDraft(): boolean {
    return this.draftContent.length > 0;
  }
}
