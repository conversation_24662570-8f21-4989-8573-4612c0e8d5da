// Example: Test the complete embedding workflow
// Run with: cargo run --example test_embedding_workflow

use ingestion_service::*;
use std::io::Write;
use tempfile::NamedTempFile;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Embedding Workflow Test ===");
    
    // Test 1: Parse email
    println!("\n1. Testing email parsing...");
    let eml_content = r#"From: <EMAIL>
To: <EMAIL>
Subject: Project Update - Q4 Deliverables

Hi <PERSON>,

I hope this email finds you well. I wanted to provide you with an update on our Q4 project deliverables.

We have successfully completed the following milestones:
- Database schema design and implementation
- API endpoint development for user authentication
- Frontend component library creation

The remaining tasks include:
- Integration testing between frontend and backend
- Performance optimization
- Security audit and vulnerability assessment

Best regards,
<PERSON>"#;

    let mut file = NamedTempFile::new()?;
    file.write_all(eml_content.as_bytes())?;
    let file_path = file.path().to_str().unwrap();

    let parsed_email = parse_eml(file_path)?;
    println!("✓ Email parsed successfully");
    println!("  Subject: {}", parsed_email.subject.as_ref().unwrap_or(&"None".to_string()));
    println!("  From: {}", parsed_email.from.as_ref().unwrap_or(&"None".to_string()));
    println!("  Cleaned text length: {} characters", 
        parsed_email.cleaned_plain_text_body.as_ref().map(|s| s.len()).unwrap_or(0));

    // Test 2: Generate embedding
    println!("\n2. Testing embedding generation...");
    let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
        .unwrap_or_else(|_| "http://localhost:8003".to_string());
    
    if let Some(cleaned_text) = &parsed_email.cleaned_plain_text_body {
        match get_embedding(cleaned_text, &embedding_service_url).await {
            Ok(embedding) => {
                println!("✓ Embedding generated successfully");
                println!("  Dimensions: {}", embedding.len());
                println!("  First 5 values: {:?}", &embedding[..5.min(embedding.len())]);
                
                // Test 3: Generate embedding for similar content
                println!("\n3. Testing similarity with related content...");
                let similar_text = "Hi team, here's an update on our project progress. We've completed the database work and API development.";
                match get_embedding(similar_text, &embedding_service_url).await {
                    Ok(similar_embedding) => {
                        let similarity = cosine_similarity(&embedding, &similar_embedding);
                        println!("✓ Similar content embedding generated");
                        println!("  Similarity score: {:.4}", similarity);
                        
                        // Test 4: Generate embedding for unrelated content
                        let unrelated_text = "The weather is nice today. I think I'll go for a walk in the park.";
                        match get_embedding(unrelated_text, &embedding_service_url).await {
                            Ok(unrelated_embedding) => {
                                let unrelated_similarity = cosine_similarity(&embedding, &unrelated_embedding);
                                println!("✓ Unrelated content embedding generated");
                                println!("  Similarity score: {:.4}", unrelated_similarity);
                                
                                if similarity > unrelated_similarity {
                                    println!("✓ Similarity test PASSED: Related content has higher similarity ({:.4} > {:.4})", 
                                        similarity, unrelated_similarity);
                                } else {
                                    println!("⚠ Similarity test warning: Expected related content to have higher similarity");
                                }
                            }
                            Err(e) => println!("✗ Failed to generate unrelated embedding: {}", e),
                        }
                    }
                    Err(e) => println!("✗ Failed to generate similar embedding: {}", e),
                }
            }
            Err(e) => {
                println!("✗ Failed to generate embedding: {}", e);
                println!("  Make sure the embedding service is running on {}", embedding_service_url);
                return Err(e);
            }
        }
    } else {
        println!("✗ No cleaned text available for embedding");
    }

    println!("\n=== Test Summary ===");
    println!("✓ Email parsing: Working");
    println!("✓ Text cleaning: Working");
    println!("✓ Embedding generation: Working");
    println!("✓ Similarity calculation: Working");
    println!("\n🎉 The embedding workflow integration is ready!");

    Ok(())
}

// Helper function to calculate cosine similarity
fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    if a.len() != b.len() {
        return 0.0;
    }
    
    let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
    let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
    
    if norm_a == 0.0 || norm_b == 0.0 {
        0.0
    } else {
        dot_product / (norm_a * norm_b)
    }
}
