// File Inspector - Examine the first part of large email files to understand format
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, BufRead};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = std::env::args().collect();
    if args.len() != 2 {
        eprintln!("Usage: {} <file_path>", args[0]);
        std::process::exit(1);
    }
    
    let file_path = &args[1];
    println!("Inspecting file: {}", file_path);
    
    // Check file size
    let metadata = std::fs::metadata(file_path)?;
    let file_size = metadata.len();
    println!("File size: {} bytes ({:.1} MB)", file_size, file_size as f64 / (1024.0 * 1024.0));
    
    // Read first 5000 bytes as raw bytes
    let mut file = File::open(file_path)?;
    let mut buffer = vec![0u8; 5000];
    let bytes_read = file.read(&mut buffer)?;
    buffer.truncate(bytes_read);
    
    println!("\n=== RAW BYTES ANALYSIS ===");
    println!("Read {} bytes", bytes_read);
    
    // Check for null bytes (binary content)
    let null_count = buffer.iter().filter(|&&b| b == 0).count();
    println!("Null bytes found: {}", null_count);
    
    // Try to convert to string
    let content = String::from_utf8_lossy(&buffer);
    let lines: Vec<&str> = content.lines().collect();
    
    println!("\n=== FIRST 20 LINES ===");
    for (i, line) in lines.iter().take(20).enumerate() {
        println!("{:3}: {}", i + 1, line);
    }
    
    println!("\n=== SEPARATOR ANALYSIS ===");
    // Look for common mbox separators
    let from_space_count = lines.iter().filter(|line| line.starts_with("From ")).count();
    let from_dash_count = lines.iter().filter(|line| line.starts_with("From - ")).count();
    let x_mozilla_count = lines.iter().filter(|line| line.to_lowercase().starts_with("x-mozilla")).count();
    
    println!("Lines starting with 'From ': {}", from_space_count);
    println!("Lines starting with 'From - ': {}", from_dash_count);
    println!("Lines starting with 'X-Mozilla': {}", x_mozilla_count);
    
    // Look for email headers
    let subject_count = lines.iter().filter(|line| line.to_lowercase().starts_with("subject:")).count();
    let message_id_count = lines.iter().filter(|line| line.to_lowercase().starts_with("message-id:")).count();
    let date_count = lines.iter().filter(|line| line.to_lowercase().starts_with("date:")).count();
    
    println!("Subject headers: {}", subject_count);
    println!("Message-ID headers: {}", message_id_count);
    println!("Date headers: {}", date_count);
    
    println!("\n=== POTENTIAL SEPARATORS ===");
    for (i, line) in lines.iter().enumerate() {
        if line.starts_with("From ") || line.starts_with("From - ") {
            println!("Line {}: {}", i + 1, line);
        }
    }
    
    Ok(())
}
