# AI-Assisted Email Response System - Compliance Guide

## Overview

This guide covers the compliance features implemented in Phase 7 to ensure adherence to GDPR and EU AI Act regulations. The system implements comprehensive audit logging and pseudonymisation capabilities to support regulatory compliance.

## Compliance Features

### 1. EU AI Act Compliance

The system is classified as **Limited-Risk AI** under the EU AI Act with the following compliance measures:

#### Human-in-the-Loop (HITL)
- ✅ **Manual Review Required**: All generated drafts require human review before sending
- ✅ **No Automated Sending**: The system cannot automatically send emails
- ✅ **Desktop Application**: Users manually copy/paste drafts into their email client

#### Output Logging (6+ Months Retention)
- ✅ **Comprehensive Audit Trail**: All RAG requests and responses are logged
- ✅ **Transaction Tracking**: Unique transaction IDs for request correlation
- ✅ **Structured JSON Logging**: Machine-readable logs for compliance reporting
- ✅ **Automatic Log Rotation**: Prevents disk space issues while maintaining retention

#### Transparency and Accountability
- ✅ **Source Citations**: Retrieved context emails are included in responses
- ✅ **Model Metadata**: LLM model and processing details are logged
- ✅ **Processing Times**: Performance metrics for audit purposes

### 2. GDPR Compliance Preparation

#### Pseudonymisation Infrastructure
- ✅ **Configurable Pseudonymisation**: Multiple levels (disabled, basic, moderate, aggressive)
- ✅ **Pattern-Based Replacement**: Email addresses, phone numbers, names, addresses
- ✅ **Deterministic Pseudonymisation**: Consistent replacements within sessions
- ✅ **Cloud Service Protection**: Pseudonymise data before sending to external services

#### Data Minimisation
- ✅ **Content Length Logging**: Log content length instead of full content where appropriate
- ✅ **Preview Truncation**: Log only content previews for audit purposes
- ✅ **Configurable Retention**: Adjustable log retention periods

## Configuration

### Environment Variables

#### Compliance Logging
```bash
# Compliance Logging Configuration (EU AI Act)
COMPLIANCE_LOG_DIR=logs                    # Directory for compliance logs
COMPLIANCE_LOG_LEVEL=INFO                  # Logging level (DEBUG, INFO, WARNING, ERROR)
COMPLIANCE_LOG_MAX_SIZE=104857600          # Max log file size (100MB)
COMPLIANCE_LOG_BACKUP_COUNT=50             # Number of backup files (6+ months)
COMPLIANCE_LOG_RETENTION_DAYS=200          # Log retention period (200 days)
```

#### Pseudonymisation
```bash
# Pseudonymisation Configuration (GDPR)
PSEUDONYMISATION_ENABLED=false             # Enable/disable pseudonymisation
PSEUDONYMISATION_LEVEL=disabled            # disabled, basic, moderate, aggressive
PSEUDONYMISATION_PRESERVE_STRUCTURE=true   # Maintain text structure
PSEUDONYMISATION_DETERMINISTIC=true        # Consistent replacements
PSEUDONYMISATION_SESSION_KEY=              # Optional session key for deterministic mode

# Custom Patterns (optional)
PSEUDO_PATTERN_EMAIL=custom_email_pattern
PSEUDO_PATTERN_PHONE=custom_phone_pattern
```

### Pseudonymisation Levels

#### Disabled
- No pseudonymisation applied
- Original text returned unchanged

#### Basic
- Email addresses: `<EMAIL>` → `[EMAIL_abc123]`

#### Moderate
- Email addresses: `<EMAIL>` → `[EMAIL_abc123]`
- Phone numbers: `************` → `[PHONE_def456]`
- Potential names: `John Smith` → `[NAME_ghi789]`

#### Aggressive
- All moderate patterns plus:
- Addresses: `123 Main Street` → `[ADDRESS_jkl012]`
- Social Security Numbers: `***********` → `[SSN_mno345]`

## Usage

### 1. Compliance Logging

The compliance logger automatically captures all RAG pipeline activities:

```python
from compliance_logger import get_compliance_logger

# Get the global compliance logger
logger = get_compliance_logger()

# Generate unique transaction ID
transaction_id = logger.generate_transaction_id()

# Log various events (automatically done by RAG pipeline)
logger.log_draft_request(transaction_id, request_data)
logger.log_context_retrieval(transaction_id, embedding_info, retrieved_emails, metadata)
logger.log_prompt_composition(transaction_id, template, context_summary, prompt_length)
logger.log_llm_generation(transaction_id, llm_metadata, generation_result)
logger.log_draft_response(transaction_id, final_response, processing_metadata)
```

### 2. Pseudonymisation

```python
from pseudonymizer import get_pseudonymizer, pseudonymize_text

# Simple text pseudonymisation
pseudonymised_text = pseudonymize_text("Contact <EMAIL>")

# Email content pseudonymisation
from pseudonymizer import pseudonymize_email_content
pseudo_subject, pseudo_sender, pseudo_content = pseudonymize_email_content(
    subject, sender, content
)

# Batch email pseudonymisation
from pseudonymizer import pseudonymize_retrieved_emails
pseudo_emails = pseudonymize_retrieved_emails(retrieved_emails)
```

### 3. Log Analysis

Compliance logs are stored in JSON format for easy analysis:

```json
{
  "event_type": "draft_request",
  "transaction_id": "abc123-def456-ghi789",
  "timestamp": "2025-07-09T20:00:00.000Z",
  "request_data": {
    "subject": "Legal Consultation",
    "sender": "<EMAIL>",
    "content_length": 250,
    "content_preview": "Dear Legal Team..."
  },
  "log_level": "INFO",
  "logger_name": "compliance_audit",
  "module": "compliance_logger",
  "function": "log_draft_request"
}
```

## Testing

### Run Compliance Tests
```bash
cd email_assistant/rag_service
python test_compliance.py
```

### Run Integration Tests
```bash
cd email_assistant/rag_service
python test_rag_compliance.py
```

## File Structure

```
email_assistant/
├── rag_service/
│   ├── compliance_logger.py      # Compliance logging implementation
│   ├── pseudonymizer.py          # Pseudonymisation implementation
│   ├── rag_pipeline.py           # Updated with compliance integration
│   ├── main.py                   # FastAPI service with compliance
│   ├── test_compliance.py        # Compliance feature tests
│   ├── test_rag_compliance.py    # Integration tests
│   └── .env.example              # Updated with compliance config
├── logs/                         # Compliance log directory
│   └── compliance_audit.log      # Main compliance log file
└── COMPLIANCE_GUIDE.md           # This documentation
```

## Regulatory Compliance Status

### EU AI Act
- ✅ **Classification**: Limited-Risk AI System
- ✅ **Human Oversight**: Mandatory human review before email sending
- ✅ **Output Logging**: 6+ months retention with structured audit trail
- ✅ **Transparency**: Source citations and model metadata included
- ✅ **Risk Assessment**: Regular evaluation capabilities built-in

### GDPR
- ✅ **Pseudonymisation Infrastructure**: Ready for cloud service protection
- ✅ **Data Minimisation**: Content length logging, preview truncation
- ✅ **Audit Trail**: Comprehensive logging for data subject rights
- ⚠️ **Full Anonymisation**: Requires advanced ML techniques (future enhancement)

## Maintenance

### Log Rotation
- Automatic rotation at 100MB per file
- 50 backup files maintained (approximately 6+ months)
- Manual cleanup scripts available if needed

### Configuration Updates
- Update `.env` file with new compliance settings
- Restart RAG service to apply changes
- Test with `test_compliance.py` after configuration changes

### Monitoring
- Monitor log file sizes and rotation
- Regular compliance audits using log analysis tools
- Performance impact assessment of logging overhead

## Future Enhancements

1. **Advanced Anonymisation**: ML-based anonymisation for full GDPR compliance
2. **Real-time Monitoring**: Dashboard for compliance metrics
3. **Automated Reporting**: Generate compliance reports for auditors
4. **Data Subject Rights**: Tools for data access, rectification, and erasure
5. **Bias Detection**: Automated bias detection in LLM outputs
