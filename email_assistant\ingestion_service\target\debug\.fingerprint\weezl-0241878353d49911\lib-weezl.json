{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 15657897354478470176, "path": 5154594156542410123, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\weezl-0241878353d49911\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}