# PostgreSQL 16 Setup Script for Windows
# AI-Assisted Email Response System

Write-Host "Setting up PostgreSQL 16 with pgvector extension..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as administrator." -ForegroundColor Red
    exit 1
}

# Install Chocolatey if not present
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey package manager..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Install PostgreSQL 16
Write-Host "Installing PostgreSQL 16..." -ForegroundColor Yellow
choco install postgresql16 --params '/Password:email_password' -y

# Wait for installation to complete
Start-Sleep -Seconds 30

# Set environment variables
$env:PGPASSWORD = "email_password"
$pgPath = "C:\Program Files\PostgreSQL\16\bin"
$env:PATH += ";$pgPath"

# Create database and user
Write-Host "Creating database and user..." -ForegroundColor Yellow
& "$pgPath\createdb.exe" -U postgres email_db
& "$pgPath\psql.exe" -U postgres -c "CREATE USER email_user WITH PASSWORD 'email_password';"
& "$pgPath\psql.exe" -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE email_db TO email_user;"

# Note: pgvector installation requires compilation on Windows
Write-Host "PostgreSQL 16 installation complete!" -ForegroundColor Green
Write-Host "Note: pgvector extension requires manual compilation on Windows." -ForegroundColor Yellow
Write-Host "Please follow the pgvector Windows installation guide at: https://github.com/pgvector/pgvector" -ForegroundColor Yellow

# Create database configuration
$configPath = Join-Path $PSScriptRoot "..\config\database.env"
@"
DATABASE_URL=postgresql://email_user:email_password@localhost:5432/email_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=email_db
POSTGRES_USER=email_user
POSTGRES_PASSWORD=email_password
"@ | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "Database configuration saved to: $configPath" -ForegroundColor Green
