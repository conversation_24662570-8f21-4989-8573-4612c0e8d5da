# Thunderbird Email Processor - Usage Example

This document provides a complete example of how to use the Thunderbird Email Processor to convert your Thunderbird emails into an enhanced format for the AI Email Assistant.

## Quick Start

### 1. Build the Processor
```bash
cd email_assistant/ingestion_service
cargo build --release --bin thunderbird_processor
```

### 2. Locate Your Thunderbird Profile
Find your Thunderbird profile directory:

**Windows:**
```
C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\[profile-name]\Mail\Local Folders
```

**macOS:**
```
~/Library/Thunderbird/Profiles/[profile-name]/Mail/Local Folders
```

**Linux:**
```
~/.thunderbird/[profile-name]/Mail/Local Folders
```

### 3. Run the Processor
```bash
./target/release/thunderbird_processor \
    "C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders" \
    "processed_emails.mbox"
```

## Expected Output

The processor will display progress information:

```
Thunderbird Email Processor
===========================
Input directory: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders
Output file: processed_emails.mbox

Processing Thunderbird directory: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders
Found 3 Thunderbird files to process
Processing file: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Inbox (type: Inbox)
  Parsed 1247 emails from C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Inbox
Processing file: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Sent (type: Sent)
  Parsed 892 emails from C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Sent
Processing file: C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Drafts (type: Drafts)
  Parsed 23 emails from C:\Users\<USER>\AppData\Roaming\Thunderbird\Profiles\abc123.default\Mail\Local Folders\Drafts
Parsed 2162 emails total
Applying email threading...
Created 847 conversation threads
Detecting duplicate emails...

=== Processing Summary ===
Total emails found: 2162
Emails processed: 2089
Duplicates removed: 73
Errors encountered: 0
Conversation threads: 847
Output file: processed_emails.mbox
========================

Processing completed successfully!
```

## Understanding the Results

### Email Threading
The processor groups related emails into conversation threads using:
- **Message-ID headers**: Unique identifiers for each email
- **In-Reply-To headers**: Direct reply relationships
- **References headers**: Full conversation chains
- **Subject line matching**: Fallback for emails with missing headers

### Email Weighting
Emails are categorized and weighted for optimal AI assistant performance:
- **Sent emails (66% weight)**: Your outgoing emails provide the best examples of your writing style
- **Received emails (34% weight)**: Incoming emails provide context and reference material
- **Draft emails (10% weight)**: Unfinished emails have lower relevance

### Duplicate Detection
The processor identifies and removes duplicate emails based on:
- Content hashing (SHA-256)
- Sender, subject, and timestamp comparison
- Body content similarity

## Enhanced mbox Format

The output file contains standard mbox format with additional X-headers:

```
From <EMAIL> Mon Jan 15 10:00:00 2024
Message-ID: <<EMAIL>>
Date: Mon, 15 Jan 2024 10:00:00 +0000
From: <EMAIL>
To: <EMAIL>, <EMAIL>
Subject: Project Update Meeting
In-Reply-To: <<EMAIL>>
References: <<EMAIL>> <<EMAIL>>
X-Thread-ID: 550e8400-e29b-41d4-a716-446655440000
X-Conversation-ID: 550e8400-e29b-41d4-a716-446655440000
X-Thread-Position: 2
X-Normalized-Subject: project update meeting
X-Email-Type: sent
X-Email-Weight: 0.66
X-Original-Folder: Sent Items
X-Content-Hash: abc123def456789...
X-Is-Duplicate: false
X-Processed-At: 2024-01-15T10:00:00Z
X-Internal-ID: 123e4567-e89b-12d3-a456-426614174000

Hi team,

Let's schedule the project update meeting for next Tuesday at 2 PM.

Best regards,
Alice
```

## Integration with AI Email Assistant

### 1. Ingest into Qdrant
```bash
# Start the ingestion service
cd email_assistant/ingestion_service
cargo run --bin ingestion_server

# Use the API to ingest the processed emails
curl -X POST "http://localhost:8000/ingest/mbox" \
     -H "Content-Type: application/json" \
     -d '{"file_path": "processed_emails.mbox"}'
```

### 2. Verify Ingestion
```bash
# Check ingested email count
curl "http://localhost:8000/messages/recent?limit=10"

# Search for specific emails
curl "http://localhost:8000/search" \
     -H "Content-Type: application/json" \
     -d '{"query": "project update meeting", "limit": 5}'
```

## Troubleshooting

### Common Issues

**"No Thunderbird files found"**
- Verify the path points to the correct Thunderbird profile directory
- Ensure the directory contains .mbox files or .sbd subdirectories
- Check file permissions

**"Failed to parse email"**
- Some emails may have corrupted headers or content
- The processor continues with other emails
- Check the error count in the summary

**High duplicate count**
- May indicate multiple exports of the same mailbox
- Review your Thunderbird export process
- Consider the duplicate detection threshold

### Performance Tips

**Large mailboxes (>10,000 emails):**
- Process in smaller batches by organizing emails into subdirectories
- Use SSD storage for better I/O performance
- Monitor memory usage during processing

**Slow threading:**
- Complex email chains with many participants may take longer
- Consider adjusting the threading algorithm parameters
- Use the debug mode to identify bottlenecks

## Next Steps

After processing your emails:

1. **Ingest into the AI system** using the ingestion service
2. **Test the AI assistant** with your processed email data
3. **Fine-tune weighting** based on your specific use case
4. **Set up automated processing** for new emails

## Support

For issues or questions:
- Check the main README for system requirements
- Review the test cases in `src/test_data.rs` for examples
- Enable debug logging with `RUST_LOG=debug` for detailed output
