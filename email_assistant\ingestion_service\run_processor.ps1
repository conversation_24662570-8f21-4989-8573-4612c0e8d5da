# Thunderbird Email Processor - PowerShell Runner
# Usage: .\run_processor.ps1 "input_directory" "output_file.mbox"

param(
    [Parameter(Mandatory=$true, Position=0)]
    [string]$InputDirectory,
    
    [Parameter(Mandatory=$true, Position=1)]
    [string]$OutputFile
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Thunderbird Email Processor" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Input Directory: $InputDirectory" -ForegroundColor Yellow
Write-Host "Output File: $OutputFile" -ForegroundColor Yellow
Write-Host ""

# Check if input directory exists
if (-not (Test-Path $InputDirectory)) {
    Write-Host "ERROR: Input directory does not exist: $InputDirectory" -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if the executable exists
$ExecutablePath = "target\release\thunderbird_processor.exe"
if (-not (Test-Path $ExecutablePath)) {
    Write-Host "ERROR: Thunderbird processor executable not found." -ForegroundColor Red
    Write-Host "Please build the project first with: cargo build --release --bin thunderbird_processor" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Starting email processing..." -ForegroundColor Green
Write-Host ""

# Run the processor
try {
    $process = Start-Process -FilePath $ExecutablePath -ArgumentList "`"$InputDirectory`"", "`"$OutputFile`"" -Wait -PassThru -NoNewWindow
    $exitCode = $process.ExitCode
    
    Write-Host ""
    if ($exitCode -eq 0) {
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Processing completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "Output file: $OutputFile" -ForegroundColor Yellow
        
        # Show file size if it exists
        if (Test-Path $OutputFile) {
            $fileSize = (Get-Item $OutputFile).Length
            $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
            Write-Host "File size: $fileSize bytes ($fileSizeMB MB)" -ForegroundColor Cyan
            
            # Show first few lines as preview
            Write-Host ""
            Write-Host "Preview of output file (first 10 lines):" -ForegroundColor Cyan
            Write-Host "----------------------------------------" -ForegroundColor Gray
            Get-Content $OutputFile -TotalCount 10 | ForEach-Object { Write-Host $_ -ForegroundColor Gray }
            Write-Host "----------------------------------------" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "You can now use this file with the AI Email Assistant ingestion service." -ForegroundColor Green
        Write-Host ""
        
        # Offer to open the output directory
        $outputDir = Split-Path $OutputFile -Parent
        if ($outputDir -and (Test-Path $outputDir)) {
            $openDir = Read-Host "Would you like to open the output directory? (y/n)"
            if ($openDir -eq 'y' -or $openDir -eq 'Y') {
                Start-Process explorer.exe -ArgumentList $outputDir
            }
        }
        
    } else {
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "Processing failed with error code: $exitCode" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
        Write-Host ""
    }
} catch {
    Write-Host "ERROR: Failed to run the processor: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Read-Host "Press Enter to exit"
