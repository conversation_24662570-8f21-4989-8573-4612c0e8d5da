// AI-Assisted Email Response System - TypeScript Type Definitions

export interface ServiceHealthResponse {
  service: string;
  status: string;
  message: string;
}

export interface EmailProcessingRequest {
  file_path: string;
  file_type: string;
}

export interface EmailProcessingResponse {
  success: boolean;
  message: string;
  processed_count?: number;
}

export interface EmailSummary {
  id: string;
  subject?: string;
  from_address?: string;
  to_addresses: string[];
  sent_date?: string;
  created_at: string;
  preview?: string;
  thread_id?: string;
  conversation_id?: string;
  email_type?: string;
  is_duplicate?: boolean;
}

export interface EmailListResponse {
  emails: EmailSummary[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface EmailDetailResponse {
  id: string;
  subject?: string;
  from_address?: string;
  to_addresses: string[];
  sent_date?: string;
  created_at: string;
  plain_text_content?: string;
  file_path?: string;
}

export interface DraftRequest {
  subject: string;
  sender: string;
  content: string;
}

export interface DraftResponse {
  draft: string;
  context_emails_count: number;
  similar_emails: any[];
  metadata: any;
}

// Threading and conversation interfaces
export interface ThreadEmail {
  id: string;
  subject: string;
  from_address: string;
  to_addresses: string[];
  sent_date: string;
  cleaned_plain_text_body: string;
  thread_position?: number;
  email_type: string;
  email_weight?: number;
}

export interface ConversationThreadResponse {
  thread_id: string;
  emails: ThreadEmail[];
  total_count: number;
}

export interface CaseEmailsResponse {
  case_id: string;
  emails: EmailSummary[];
  total_count: number;
}

// Request interface types
export interface RequestMode {
  id: 'general' | 'case-specific' | 'draft-reply';
  label: string;
  description: string;
}

export interface ResponseContext {
  id: 'lawyer-to-insurance' | 'insurance-to-lawyer' | 'general';
  label: string;
  description: string;
}

// UI State interfaces
export interface EmailListState {
  emails: EmailSummary[];
  loading: boolean;
  error?: string;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  selectedEmailId?: string;
}

export interface EmailDetailState {
  email?: EmailDetailResponse;
  loading: boolean;
  error?: string;
}

export interface DraftState {
  content?: string;
  loading: boolean;
  error?: string;
  lastGenerated?: Date;
}

export interface AppState {
  emailList: EmailListState;
  emailDetail: EmailDetailState;
  draft: DraftState;
  services: {
    ingestion: ServiceHealthResponse | null;
    rag: ServiceHealthResponse | null;
    embedding: ServiceHealthResponse | null;
  };
}
