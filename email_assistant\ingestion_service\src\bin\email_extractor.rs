// Email Extractor - Extract specific email by number from mbox file
use std::fs::File;
use std::io::{<PERSON>uf<PERSON><PERSON><PERSON>, Read};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = std::env::args().collect();
    if args.len() != 3 {
        eprintln!("Usage: {} <file_path> <email_number>", args[0]);
        std::process::exit(1);
    }
    
    let file_path = &args[1];
    let target_email_num: usize = args[2].parse()?;
    
    println!("Extracting email #{} from: {}", target_email_num, file_path);
    
    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut total_emails_found = 0;
    let mut buffer = [0; 8192];
    let mut leftover_bytes = Vec::new();

    loop {
        let bytes_read = reader.read(&mut buffer)?;
        if bytes_read == 0 {
            break; // End of file
        }

        // Combine leftover bytes from previous iteration with new bytes
        let mut all_bytes = leftover_bytes.clone();
        all_bytes.extend_from_slice(&buffer[..bytes_read]);
        leftover_bytes.clear();

        let mut line_start = 0;
        for i in 0..all_bytes.len() {
            if all_bytes[i] == b'\n' || i == all_bytes.len() - 1 {
                let line_end = if i == all_bytes.len() - 1 && bytes_read < buffer.len() {
                    i + 1
                } else if all_bytes[i] == b'\n' {
                    i
                } else {
                    // Incomplete line, save for next iteration
                    leftover_bytes.extend_from_slice(&all_bytes[line_start..]);
                    break;
                };

                let line_bytes = &all_bytes[line_start..line_end];
                let line_str = String::from_utf8_lossy(line_bytes);
                let line_str = line_str.trim_end_matches('\r');

                // Check for mbox separator lines
                if (line_str.starts_with("From ") && line_str.contains("@")) ||
                   line_str.starts_with("From - ") {
                    // Process the previous email if we have one
                    if in_email && !current_email_bytes.is_empty() {
                        total_emails_found += 1;
                        
                        if total_emails_found == target_email_num {
                            println!("\n=== EMAIL #{} FOUND ===", total_emails_found);
                            println!("Email size: {} bytes ({:.1} MB)", 
                                    current_email_bytes.len(), 
                                    current_email_bytes.len() as f64 / (1024.0 * 1024.0));
                            
                            // Convert to string and display
                            let email_content = String::from_utf8_lossy(&current_email_bytes);
                            println!("\n=== EMAIL CONTENT ===");
                            println!("{}", email_content);
                            return Ok(());
                        }
                        
                        current_email_bytes.clear();
                    }

                    // Start new email
                    in_email = true;
                    current_email_bytes.extend_from_slice(line_bytes);
                    current_email_bytes.push(b'\n');
                } else if in_email {
                    // Add line to current email
                    current_email_bytes.extend_from_slice(line_bytes);
                    current_email_bytes.push(b'\n');
                }

                line_start = i + 1;
            }
        }

        // Save incomplete line for next iteration
        if line_start < all_bytes.len() {
            leftover_bytes.extend_from_slice(&all_bytes[line_start..]);
        }
        
        // Progress indicator
        if total_emails_found % 1000 == 0 && total_emails_found > 0 {
            println!("Processed {} emails...", total_emails_found);
        }
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        total_emails_found += 1;
        
        if total_emails_found == target_email_num {
            println!("\n=== EMAIL #{} FOUND (LAST EMAIL) ===", total_emails_found);
            println!("Email size: {} bytes ({:.1} MB)", 
                    current_email_bytes.len(), 
                    current_email_bytes.len() as f64 / (1024.0 * 1024.0));
            
            let email_content = String::from_utf8_lossy(&current_email_bytes);
            println!("\n=== EMAIL CONTENT ===");
            println!("{}", email_content);
            return Ok(());
        }
    }

    println!("Email #{} not found. Total emails in file: {}", target_email_num, total_emails_found);
    Ok(())
}
