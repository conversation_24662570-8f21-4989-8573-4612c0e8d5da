# LLM Provider Abstraction
# This module provides a unified interface for different LLM providers

import os
import httpx
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from langchain_ollama import OllamaLLM

logger = logging.getLogger(__name__)

class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    async def generate(self, prompt: str) -> str:
        """Generate text from the given prompt"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the provider is healthy and available"""
        pass

class OllamaProvider(LLMProvider):
    """Ollama LLM provider"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "mistral:7b", temperature: float = 0.7):
        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        self.llm = OllamaLLM(
            base_url=base_url,
            model=model,
            temperature=temperature
        )
        
    async def generate(self, prompt: str) -> str:
        """Generate text using Ollama"""
        try:
            response = await self.llm.ainvoke(prompt)
            return response
        except Exception as e:
            logger.error(f"Ollama generation error: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Ollama model information"""
        return {
            "provider": "ollama",
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }
    
    async def health_check(self) -> bool:
        """Check Ollama health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/tags")
                return response.status_code == 200
        except Exception:
            return False

class InitiumProvider(LLMProvider):
    """Initium LLM provider"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.initium.ai/v1", model: str = "initium-7b", temperature: float = 0.7):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    async def generate(self, prompt: str) -> str:
        """Generate text using Initium API"""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 2000
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    raise Exception(f"Initium API error: {response.status_code} - {response.text}")
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"Initium generation error: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Initium model information"""
        return {
            "provider": "initium",
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }
    
    async def health_check(self) -> bool:
        """Check Initium API health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers=self.headers
                )
                return response.status_code == 200
        except Exception:
            return False

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider (for future use)"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.base_url = "https://api.openai.com/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    async def generate(self, prompt: str) -> str:
        """Generate text using OpenAI API"""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 2000
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get OpenAI model information"""
        return {
            "provider": "openai",
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }
    
    async def health_check(self) -> bool:
        """Check OpenAI API health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers=self.headers
                )
                return response.status_code == 200
        except Exception:
            return False

def create_llm_provider(provider_type: str, **kwargs) -> LLMProvider:
    """Factory function to create LLM providers"""
    
    if provider_type.lower() == "ollama":
        return OllamaProvider(
            base_url=kwargs.get("base_url", "http://localhost:11434"),
            model=kwargs.get("model", "mistral:7b"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    elif provider_type.lower() == "initium":
        api_key = kwargs.get("api_key") or os.getenv("INITIUM_API_KEY")
        if not api_key:
            raise ValueError("Initium API key is required")
        
        return InitiumProvider(
            api_key=api_key,
            base_url=kwargs.get("base_url", "https://api.initium.ai/v1"),
            model=kwargs.get("model", "initium-7b"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    elif provider_type.lower() == "openai":
        api_key = kwargs.get("api_key") or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        return OpenAIProvider(
            api_key=api_key,
            model=kwargs.get("model", "gpt-3.5-turbo"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    else:
        raise ValueError(f"Unsupported LLM provider: {provider_type}")
