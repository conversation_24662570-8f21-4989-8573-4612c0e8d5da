{"name": "desktop_app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "devDependencies": {"@tauri-apps/cli": "^2.6.2", "typescript": "~5.8.3", "vite": "^7.0.3"}, "dependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-dialog": "^2.3.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-http": "^2.5.0"}}