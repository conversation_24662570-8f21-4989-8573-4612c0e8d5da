# Ollama Model Selection
# This module provides easy model selection for Ollama

import os
import httpx
import json
import logging
from typing import Dict, Any, Optional, List
from langchain_ollama import OllamaLLM

logger = logging.getLogger(__name__)

class OllamaModelManager:
    """Manages Ollama models and provides easy switching between them"""

    def __init__(self, base_url: str = "http://localhost:11434", temperature: float = 0.7):
        self.base_url = base_url
        self.temperature = temperature
        self.current_model = None
        self.llm = None

    async def list_available_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/tags")
                if response.status_code == 200:
                    data = response.json()
                    models = [model["name"] for model in data.get("models", [])]
                    return models
                return []
        except Exception as e:
            logger.error(f"Error fetching Ollama models: {e}")
            return []

    def set_model(self, model_name: str):
        """Set the current model"""
        self.current_model = model_name
        self.llm = OllamaLLM(
            base_url=self.base_url,
            model=model_name,
            temperature=self.temperature
        )
        logger.info(f"Switched to Ollama model: {model_name}")

    async def generate(self, prompt: str) -> str:
        """Generate text using the current model"""
        if not self.llm:
            raise ValueError("No model selected. Call set_model() first.")

        try:
            response = await self.llm.ainvoke(prompt)
            return response
        except Exception as e:
            logger.error(f"Ollama generation error with model {self.current_model}: {e}")
            raise

    def get_model_info(self) -> Dict[str, Any]:
        """Get current model information"""
        return {
            "provider": "ollama",
            "model": self.current_model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }

    async def health_check(self) -> bool:
        """Check Ollama health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/tags")
                return response.status_code == 200
        except Exception:
            return False

class InitiumProvider(LLMProvider):
    """Initium LLM provider"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.initium.ai/v1", model: str = "initium-7b", temperature: float = 0.7):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    async def generate(self, prompt: str) -> str:
        """Generate text using Initium API"""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 2000
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    raise Exception(f"Initium API error: {response.status_code} - {response.text}")
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"Initium generation error: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Initium model information"""
        return {
            "provider": "initium",
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }
    
    async def health_check(self) -> bool:
        """Check Initium API health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers=self.headers
                )
                return response.status_code == 200
        except Exception:
            return False

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider (for future use)"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.base_url = "https://api.openai.com/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    async def generate(self, prompt: str) -> str:
        """Generate text using OpenAI API"""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 2000
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get OpenAI model information"""
        return {
            "provider": "openai",
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature
        }
    
    async def health_check(self) -> bool:
        """Check OpenAI API health"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers=self.headers
                )
                return response.status_code == 200
        except Exception:
            return False

def create_llm_provider(provider_type: str, **kwargs) -> LLMProvider:
    """Factory function to create LLM providers"""
    
    if provider_type.lower() == "ollama":
        return OllamaProvider(
            base_url=kwargs.get("base_url", "http://localhost:11434"),
            model=kwargs.get("model", "mistral:7b"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    elif provider_type.lower() == "initium":
        api_key = kwargs.get("api_key") or os.getenv("INITIUM_API_KEY")
        if not api_key:
            raise ValueError("Initium API key is required")
        
        return InitiumProvider(
            api_key=api_key,
            base_url=kwargs.get("base_url", "https://api.initium.ai/v1"),
            model=kwargs.get("model", "initium-7b"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    elif provider_type.lower() == "openai":
        api_key = kwargs.get("api_key") or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        return OpenAIProvider(
            api_key=api_key,
            model=kwargs.get("model", "gpt-3.5-turbo"),
            temperature=kwargs.get("temperature", 0.7)
        )
    
    else:
        raise ValueError(f"Unsupported LLM provider: {provider_type}")
