"""
Compliance Logger for AI-Assisted Email Response System

This module implements comprehensive logging for GDPR and EU AI Act compliance.
It provides structured JSON logging with transaction IDs, timestamps, and 
proper log rotation for audit trail requirements.

Key Features:
- Unique transaction ID generation for request correlation
- Structured JSON logging for audit trails
- Automatic log rotation and retention (6+ months for EU AI Act)
- Comprehensive request/response logging for /generate_draft endpoint
- Secure log file handling with proper permissions
"""

import logging
import logging.handlers
import json
import uuid
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path


class ComplianceLogger:
    """
    Compliance logger for RAG service audit trails
    
    Implements structured logging with transaction tracking for regulatory compliance.
    Logs are stored in JSON format with automatic rotation and retention policies.
    """
    
    def __init__(
        self,
        log_dir: str = "logs",
        log_level: str = "INFO",
        max_file_size: int = 100 * 1024 * 1024,  # 100MB
        backup_count: int = 50,  # Keep 50 backup files (6+ months)
        retention_days: int = 200  # 200 days retention (6+ months)
    ):
        """
        Initialize the compliance logger

        Args:
            log_dir: Directory for log files
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
            max_file_size: Maximum size per log file in bytes
            backup_count: Number of backup files to keep
            retention_days: Number of days to retain logs
        """
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.retention_days = retention_days

        # Create log directory if it doesn't exist
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # Set up the logger
        self.logger = self._setup_logger()

    def close(self):
        """Close all handlers to release file locks"""
        for handler in self.logger.handlers[:]:
            handler.close()
            self.logger.removeHandler(handler)
        
    def _setup_logger(self) -> logging.Logger:
        """Set up the compliance logger with proper handlers and formatters"""
        
        # Create logger
        logger = logging.getLogger("compliance_audit")
        logger.setLevel(self.log_level)
        
        # Prevent duplicate handlers
        if logger.handlers:
            logger.handlers.clear()
        
        # Create rotating file handler
        log_file = self.log_dir / "compliance_audit.log"
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        # Create JSON formatter
        formatter = ComplianceJSONFormatter()
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        logger.addHandler(file_handler)
        
        # Also add console handler for development
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.WARNING)  # Only warnings and errors to console
        logger.addHandler(console_handler)
        
        return logger
    
    def generate_transaction_id(self) -> str:
        """Generate a unique transaction ID for request correlation"""
        return str(uuid.uuid4())
    
    def log_draft_request(
        self,
        transaction_id: str,
        request_data: Dict[str, Any],
        user_context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log a draft generation request
        
        Args:
            transaction_id: Unique transaction identifier
            request_data: The incoming request data (subject, sender, content)
            user_context: Additional user context if available
        """
        log_entry = {
            "event_type": "draft_request",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "request_data": {
                "subject": request_data.get("subject", ""),
                "sender": request_data.get("sender", ""),
                "content_length": len(request_data.get("content", "")),
                "content_preview": request_data.get("content", "")[:200] + "..." if len(request_data.get("content", "")) > 200 else request_data.get("content", "")
            },
            "user_context": user_context or {}
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_context_retrieval(
        self,
        transaction_id: str,
        query_embedding_info: Dict[str, Any],
        retrieved_emails: List[Dict[str, Any]],
        search_metadata: Dict[str, Any]
    ) -> None:
        """
        Log context retrieval from vector search
        
        Args:
            transaction_id: Transaction identifier
            query_embedding_info: Information about the query embedding
            retrieved_emails: List of retrieved similar emails
            search_metadata: Metadata about the search operation
        """
        log_entry = {
            "event_type": "context_retrieval",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "query_embedding": {
                "dimension": query_embedding_info.get("dimension", 0),
                "model": query_embedding_info.get("model", "unknown")
            },
            "retrieved_context": {
                "email_count": len(retrieved_emails),
                "emails": [
                    {
                        "id": email.get("id", "unknown"),
                        "similarity_score": email.get("similarity_score", 0.0),
                        "subject": email.get("subject", ""),
                        "content_length": len(email.get("content", ""))
                    }
                    for email in retrieved_emails
                ]
            },
            "search_metadata": search_metadata
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_prompt_composition(
        self,
        transaction_id: str,
        prompt_template: str,
        context_summary: Dict[str, Any],
        final_prompt_length: int
    ) -> None:
        """
        Log prompt composition for LLM
        
        Args:
            transaction_id: Transaction identifier
            prompt_template: The prompt template used
            context_summary: Summary of context included in prompt
            final_prompt_length: Length of the final composed prompt
        """
        log_entry = {
            "event_type": "prompt_composition",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "prompt_info": {
                "template_id": "legal_advisor_rag_v1",
                "context_emails_count": context_summary.get("email_count", 0),
                "final_prompt_length": final_prompt_length,
                "context_summary": context_summary
            }
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_llm_generation(
        self,
        transaction_id: str,
        llm_metadata: Dict[str, Any],
        generation_result: Dict[str, Any]
    ) -> None:
        """
        Log LLM generation process and result
        
        Args:
            transaction_id: Transaction identifier
            llm_metadata: Metadata about the LLM used
            generation_result: Result of the generation process
        """
        log_entry = {
            "event_type": "llm_generation",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "llm_info": {
                "model": llm_metadata.get("model", "unknown"),
                "base_url": llm_metadata.get("base_url", "unknown"),
                "generation_time_ms": llm_metadata.get("generation_time_ms", 0)
            },
            "generation_result": {
                "draft_length": len(generation_result.get("draft", "")),
                "success": generation_result.get("success", False),
                "error": generation_result.get("error", None)
            }
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_draft_response(
        self,
        transaction_id: str,
        final_response: Dict[str, Any],
        processing_metadata: Dict[str, Any]
    ) -> None:
        """
        Log the final draft response
        
        Args:
            transaction_id: Transaction identifier
            final_response: The complete response sent to client
            processing_metadata: Metadata about the processing pipeline
        """
        log_entry = {
            "event_type": "draft_response",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "response_data": {
                "draft_length": len(final_response.get("draft", "")),
                "context_emails_count": final_response.get("context_emails_count", 0),
                "similar_emails_count": len(final_response.get("similar_emails", [])),
                "metadata": final_response.get("metadata", {})
            },
            "processing_metadata": processing_metadata,
            "compliance_info": {
                "eu_ai_act_classification": "limited_risk",
                "human_in_loop_required": True,
                "automated_sending_disabled": True
            }
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_error(
        self,
        transaction_id: str,
        error_type: str,
        error_message: str,
        error_context: Dict[str, Any]
    ) -> None:
        """
        Log errors in the compliance audit trail
        
        Args:
            transaction_id: Transaction identifier
            error_type: Type of error (e.g., "llm_error", "retrieval_error")
            error_message: Error message
            error_context: Additional context about the error
        """
        log_entry = {
            "event_type": "error",
            "transaction_id": transaction_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error_info": {
                "error_type": error_type,
                "error_message": error_message,
                "error_context": error_context
            }
        }
        
        self.logger.error(json.dumps(log_entry, ensure_ascii=False))


class ComplianceJSONFormatter(logging.Formatter):
    """Custom JSON formatter for compliance logs"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        try:
            # Try to parse the message as JSON (for structured logs)
            log_data = json.loads(record.getMessage())
            
            # Add standard logging fields
            log_data.update({
                "log_level": record.levelname,
                "logger_name": record.name,
                "module": record.module,
                "function": record.funcName,
                "line_number": record.lineno
            })
            
            return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))
            
        except (json.JSONDecodeError, ValueError):
            # Fallback for non-JSON messages
            log_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "log_level": record.levelname,
                "logger_name": record.name,
                "module": record.module,
                "function": record.funcName,
                "line_number": record.lineno,
                "message": record.getMessage()
            }
            
            return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


# Global compliance logger instance
_compliance_logger: Optional[ComplianceLogger] = None


def get_compliance_logger() -> ComplianceLogger:
    """Get the global compliance logger instance"""
    global _compliance_logger
    
    if _compliance_logger is None:
        # Initialize with environment variables or defaults
        log_dir = os.getenv("COMPLIANCE_LOG_DIR", "logs")
        log_level = os.getenv("COMPLIANCE_LOG_LEVEL", "INFO")
        max_file_size = int(os.getenv("COMPLIANCE_LOG_MAX_SIZE", str(100 * 1024 * 1024)))
        backup_count = int(os.getenv("COMPLIANCE_LOG_BACKUP_COUNT", "50"))
        retention_days = int(os.getenv("COMPLIANCE_LOG_RETENTION_DAYS", "200"))
        
        _compliance_logger = ComplianceLogger(
            log_dir=log_dir,
            log_level=log_level,
            max_file_size=max_file_size,
            backup_count=backup_count,
            retention_days=retention_days
        )
    
    return _compliance_logger
