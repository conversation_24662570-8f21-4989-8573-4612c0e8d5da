{"rustc": 1842507548689473721, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 1749457146276060112, "deps": [[2883436298747778685, "pki_types", false, 18021711389995360], [3722963349756955755, "once_cell", false, 10802249507119394238], [5491919304041016563, "ring", false, 6766109834959954104], [5986029879202738730, "log", false, 10377209137103739149], [6528079939221783635, "zeroize", false, 12838774724268550413], [16400140949089969347, "build_script_build", false, 1240582720081228705], [17003143334332120809, "subtle", false, 5656905826111867917], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 10990509437443991320]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-7546dac1fa2e1553\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}