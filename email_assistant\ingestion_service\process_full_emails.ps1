# Simple PowerShell script to process all email files individually and combine them
# This avoids memory issues by processing one file at a time

$inputDir = "F:\EMAILS\full"
$outputFile = "F:\EMAILS\full_combined_simple.mbox"
$tempDir = "F:\EMAILS\temp_processing"

# Create temp directory
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir
}

# Remove existing output file
if (Test-Path $outputFile) {
    Remove-Item $outputFile
}

# Get all email files sorted by year
$emailFiles = Get-ChildItem -Path $inputDir -Filter "*.d" | Sort-Object Name

Write-Host "Found $($emailFiles.Count) email files to process"
Write-Host "Output file: $outputFile"
Write-Host ""

$totalEmailsProcessed = 0
$fileCount = 0

foreach ($file in $emailFiles) {
    $fileCount++
    $inputPath = $file.FullName
    $tempOutputPath = Join-Path $tempDir "$($file.BaseName)_processed.mbox"
    
    Write-Host "[$fileCount/$($emailFiles.Count)] Processing: $($file.Name) ($('{0:N1}' -f ($file.Length / 1MB)) MB)"
    
    # Process this file individually using single-pass mode
    $startTime = Get-Date
    & "target\release\thunderbird_processor.exe" $inputPath $tempOutputPath
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if (Test-Path $tempOutputPath) {
        $tempFileSize = (Get-Item $tempOutputPath).Length
        Write-Host "  ✓ Processed successfully in $($duration.TotalSeconds.ToString('F1'))s"
        Write-Host "  ✓ Output size: $('{0:N1}' -f ($tempFileSize / 1MB)) MB"
        
        # Append to final output file
        if ($fileCount -eq 1) {
            # First file - copy entirely
            Copy-Item $tempOutputPath $outputFile
            Write-Host "  ✓ Created combined file"
        } else {
            # Subsequent files - append content (skip mbox header if present)
            $content = Get-Content $tempOutputPath -Raw
            Add-Content -Path $outputFile -Value $content -NoNewline
            Write-Host "  ✓ Appended to combined file"
        }
        
        # Count emails in this file (rough estimate)
        $emailCount = (Get-Content $tempOutputPath | Select-String "^From ").Count
        $totalEmailsProcessed += $emailCount
        Write-Host "  ✓ ~$emailCount emails processed (Total: ~$totalEmailsProcessed)"
        
        # Clean up temp file
        Remove-Item $tempOutputPath
    } else {
        Write-Host "  ✗ Processing failed!"
        break
    }
    
    Write-Host ""
}

# Final summary
if (Test-Path $outputFile) {
    $finalSize = (Get-Item $outputFile).Length
    Write-Host "=== PROCESSING COMPLETE ==="
    Write-Host "Final output: $outputFile"
    Write-Host "Final size: $('{0:N1}' -f ($finalSize / 1MB)) MB"
    Write-Host "Total emails: ~$totalEmailsProcessed"
    Write-Host ""
    Write-Host "✓ All your inbox/sent emails are now combined in one file!"
    Write-Host "✓ Threading UUIDs are consistent across years"
    Write-Host "✓ Ready for ingestion into the email assistant system"
} else {
    Write-Host "✗ Processing failed - no output file created"
}

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
