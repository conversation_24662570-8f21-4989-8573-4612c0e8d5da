(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))a(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const r of i.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&a(r)}).observe(document,{childList:!0,subtree:!0});function t(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(s){if(s.ep)return;s.ep=!0;const i=t(s);fetch(s.href,i)}})();async function p(n,e={},t){return window.__TAURI_INTERNALS__.invoke(n,e,t)}class w{container;emails=[];loading=!1;pageSize=20;totalCount=0;selectedEmailId=null;onEmailSelect=null;constructor(e){this.container=e,this.render(),this.loadEmails()}setOnEmailSelect(e){this.onEmailSelect=e}async loadEmails(){this.setLoading(!0);try{await this.loadAllEmails(),this.renderEmailList()}catch(e){this.renderError(`Failed to load emails: ${e}`)}finally{this.setLoading(!1)}}async loadAllEmails(){const e=[];let t=1,a=1;do{const s=await p("get_processed_emails",{page:t,pageSize:this.pageSize});e.push(...s.emails),this.totalCount=s.total_count,a=Math.ceil(s.total_count/s.page_size),t++,this.updateLoadingProgress(e.length,s.total_count)}while(t<=a);this.emails=e}updateLoadingProgress(e,t){const a=this.container.querySelector("#email-list-loading p");a&&(a.textContent=`Loading emails... ${e}/${t}`)}setLoading(e){this.loading=e,this.renderLoadingState()}render(){this.container.innerHTML=`
      <div class="email-list-container">
        <div class="email-list-header">
          <h2>Inbox Emails</h2>
          <div class="email-list-controls">
            <span class="sort-indicator">📅 Newest First</span>
            <span class="filter-indicator">📧 Inbox Only</span>
            <button id="refresh-emails" class="btn btn-secondary">
              <span class="icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
        <div class="email-list-content">
          <div id="email-list-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading emails...</p>
          </div>
          <div id="email-list-error" class="error-state" style="display: none;"></div>
          <div id="email-list-items" class="email-list-items"></div>
          <div id="email-list-pagination" class="pagination-container"></div>
        </div>
      </div>
    `,this.container.querySelector("#refresh-emails")?.addEventListener("click",()=>this.loadEmails())}renderLoadingState(){const e=this.container.querySelector("#email-list-loading"),t=this.container.querySelector("#email-list-items");this.loading?(e.style.display="flex",t.style.display="none"):(e.style.display="none",t.style.display="block")}renderError(e){const t=this.container.querySelector("#email-list-error"),a=this.container.querySelector("#email-list-items");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none"}renderEmailList(){const e=this.container.querySelector("#email-list-items"),t=this.container.querySelector("#email-list-error");if(t.style.display="none",this.emails.length===0){e.innerHTML=`
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No emails found</h3>
          <p>Process some email files to see them here.</p>
        </div>
      `;return}const a=this.emails.filter(r=>!r.email_type||r.email_type.toLowerCase()!=="sent");if(a.length===0){e.innerHTML=`
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No inbox emails found</h3>
          <p>All processed emails appear to be sent emails. Only inbox emails are displayed here.</p>
        </div>
      `;return}const s=[...a].sort((r,l)=>{const c=r.sent_date?new Date(r.sent_date).getTime():0;return(l.sent_date?new Date(l.sent_date).getTime():0)-c}),i=s.map(r=>this.renderEmailItem(r)).join("");e.innerHTML=i,e.querySelectorAll(".email-item").forEach((r,l)=>{r.addEventListener("click",()=>{this.selectEmail(s[l])}),r.addEventListener("dblclick",()=>{this.showEmailModal(s[l])})}),this.renderPagination()}renderEmailItem(e){const t=this.selectedEmailId===e.id,a=e.sent_date?new Date(e.sent_date).toLocaleDateString():"Unknown",s=e.from_address||"Unknown sender",i=e.subject||"(No subject)",r=e.preview||"No preview available",l=e.thread_id&&e.thread_id.trim()!=="",c=e.is_duplicate===!0,o=l?"🧵":"",u=c?"📋":"",d=e.email_type?`email-type-${e.email_type.toLowerCase()}`:"";return`
      <div class="email-item ${t?"selected":""} ${d}" data-email-id="${e.id}" data-thread-id="${e.thread_id||""}" data-conversation-id="${e.conversation_id||""}">
        <div class="email-item-header">
          <div class="email-subject">
            <span class="threading-indicators">
              ${o}${u}
            </span>
            ${this.escapeHtml(i)}
          </div>
          <div class="email-date">${a}</div>
        </div>
        <div class="email-from">${this.escapeHtml(s)}</div>
        <div class="email-preview">${this.escapeHtml(r)}</div>
        <div class="email-recipients">
          To: ${e.to_addresses.map(y=>this.escapeHtml(y)).join(", ")}
        </div>
        ${l?`<div class="thread-info" title="Part of conversation thread">Thread ID: ${e.thread_id}</div>`:""}
      </div>
    `}renderPagination(){const e=this.container.querySelector("#email-list-pagination"),t=this.emails.filter(a=>!a.email_type||a.email_type.toLowerCase()!=="sent");e.innerHTML=`
      <div class="pagination">
        <span class="pagination-info">
          Showing ${t.length} inbox emails of ${this.totalCount} total emails (sorted by date, newest first)
        </span>
      </div>
    `,e.style.display="flex"}selectEmail(e){this.selectedEmailId=e.id,this.renderEmailList(),this.onEmailSelect&&this.onEmailSelect(e)}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}refresh(){this.emails=[],this.loadEmails()}getSelectedEmail(){return this.emails.find(e=>e.id===this.selectedEmailId)||null}async showEmailModal(e){try{if(e.thread_id&&e.thread_id.trim()!==""){const t=await p("get_conversation_thread",{threadId:e.thread_id});this.createConversationModal(t,e.id)}else{const t=await p("get_email_details",{emailId:e.id});this.createSingleEmailModal(t)}}catch(t){console.error("Failed to load email/conversation details for modal:",t),alert(`Failed to load email details: ${t}`)}}createSingleEmailModal(e){const t=document.querySelector(".email-modal");t&&t.remove();const a=document.createElement("div");a.className="email-modal";const s=e.sent_date?new Date(e.sent_date).toLocaleString():"Unknown",i=e.subject||"(No subject)",r=e.from_address||"Unknown sender",l=e.plain_text_content||"No content available",c=e.to_addresses.join(", ")||"Unknown recipients";a.innerHTML=`
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">${this.escapeHtml(i)}</h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body">
          <div class="email-modal-metadata">
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">From:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(r)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">To:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(c)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">Date:</span>
              <span class="email-modal-metadata-value">${s}</span>
            </div>
          </div>
          <div class="email-modal-content-section">
            <pre class="email-modal-text">${this.escapeHtml(l)}</pre>
          </div>
        </div>
      </div>
    `,document.body.appendChild(a),a.querySelector(".email-modal-close")?.addEventListener("click",()=>this.closeEmailModal()),a.addEventListener("click",d=>{d.target===a&&this.closeEmailModal()});const u=d=>{d.key==="Escape"&&(this.closeEmailModal(),document.removeEventListener("keydown",u))};document.addEventListener("keydown",u),setTimeout(()=>{a.classList.add("show")},10)}createConversationModal(e,t){const a=document.querySelector(".email-modal");a&&a.remove();const s=document.createElement("div");s.className="email-modal conversation-modal";const r=[...e.emails].sort((o,u)=>{const d=new Date(o.sent_date).getTime(),y=new Date(u.sent_date).getTime();return d-y}).map((o,u)=>{const d=o.id===t,y=new Date(o.sent_date).toLocaleString(),E=o.email_type?`email-type-${o.email_type.toLowerCase()}`:"",S=o.email_weight?`(Weight: ${Math.round(o.email_weight*100)}%)`:"";return`
        <div class="conversation-email ${d?"selected-email":""} ${E}">
          <div class="conversation-email-header">
            <div class="conversation-email-meta">
              <span class="conversation-email-position">#${u+1}</span>
              <span class="conversation-email-type">${o.email_type}</span>
              ${o.email_weight?`<span class="conversation-email-weight">${S}</span>`:""}
            </div>
            <div class="conversation-email-date">${y}</div>
          </div>
          <div class="conversation-email-subject">${this.escapeHtml(o.subject)}</div>
          <div class="conversation-email-from">From: ${this.escapeHtml(o.from_address)}</div>
          <div class="conversation-email-to">To: ${o.to_addresses.map(q=>this.escapeHtml(q)).join(", ")}</div>
          <div class="conversation-email-content">
            <pre class="conversation-email-text">${this.escapeHtml(o.cleaned_plain_text_body)}</pre>
          </div>
        </div>
      `}).join("");s.innerHTML=`
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">
            Conversation Thread (${e.total_count} emails)
            <span class="thread-id">Thread: ${e.thread_id}</span>
          </h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body conversation-body">
          <div class="conversation-thread">
            ${r}
          </div>
        </div>
      </div>
    `,document.body.appendChild(s),s.querySelector(".email-modal-close")?.addEventListener("click",()=>this.closeEmailModal()),s.addEventListener("click",o=>{o.target===s&&this.closeEmailModal()});const c=o=>{o.key==="Escape"&&(this.closeEmailModal(),document.removeEventListener("keydown",c))};document.addEventListener("keydown",c),setTimeout(()=>{s.classList.add("show");const o=s.querySelector(".selected-email");o&&o.scrollIntoView({behavior:"smooth",block:"center"})},10)}closeEmailModal(){const e=document.querySelector(".email-modal");e&&(e.classList.remove("show"),setTimeout(()=>{e.remove()},150))}}class L{container;email=null;loading=!1;onGenerateDraft=null;onGenerateInformation=null;currentResponseContext="general";responseContexts=[{id:"general",label:"General Information",description:"General legal information and research"},{id:"lawyer-to-insurance",label:"Lawyer to Insurance",description:"Formal communication to insurance companies"},{id:"insurance-to-lawyer",label:"Insurance to Lawyer",description:"Business communication from insurance perspective"}];constructor(e){this.container=e,this.render()}setOnGenerateDraft(e){this.onGenerateDraft=e}setOnGenerateInformation(e){this.onGenerateInformation=e}async loadEmail(e){this.setLoading(!0);try{const t=await p("get_email_details",{emailId:e.id});this.email=t,this.renderEmailDetail()}catch(t){this.renderError(`Failed to load email details: ${t}`)}finally{this.setLoading(!1)}}setLoading(e){this.loading=e,this.renderLoadingState()}render(){this.container.innerHTML=`
      <div class="email-detail-container">
        <div class="email-detail-header">
          <h2>Email Details</h2>
          <div class="email-detail-controls">
            <div class="response-context-selector" style="display: none;">
              <label for="email-response-context">Response Context:</label>
              <select id="email-response-context" class="context-select">
                ${this.responseContexts.map(s=>`
                  <option value="${s.id}" ${s.id===this.currentResponseContext?"selected":""}>
                    ${s.label}
                  </option>
                `).join("")}
              </select>
            </div>
          </div>
          <div class="email-detail-actions">
            <button id="generate-info-btn" class="btn btn-secondary" style="display: none;">
              <span class="icon">📋</span>
              Get Information
            </button>
            <button id="generate-draft-btn" class="btn btn-primary" style="display: none;">
              <span class="icon">✨</span>
              Generate Draft Reply
            </button>
          </div>
        </div>
        <div class="email-detail-content">
          <div id="email-detail-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading email details...</p>
          </div>
          <div id="email-detail-error" class="error-state" style="display: none;"></div>
          <div id="email-detail-info" class="email-detail-info" style="display: none;"></div>
        </div>
      </div>
    `;const e=this.container.querySelector("#generate-draft-btn"),t=this.container.querySelector("#generate-info-btn"),a=this.container.querySelector("#email-response-context");e?.addEventListener("click",()=>{this.email&&this.onGenerateDraft&&this.onGenerateDraft(this.email.id)}),t?.addEventListener("click",()=>{this.email&&this.onGenerateInformation&&this.onGenerateInformation(this.email.id,this.currentResponseContext)}),a?.addEventListener("change",()=>{this.currentResponseContext=a.value})}renderLoadingState(){const e=this.container.querySelector("#email-detail-loading"),t=this.container.querySelector("#email-detail-info"),a=this.container.querySelector("#email-detail-error");this.loading?(e.style.display="flex",t.style.display="none",a.style.display="none"):e.style.display="none"}renderError(e){const t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#email-detail-info"),s=this.container.querySelector("#generate-draft-btn");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none",s.style.display="none"}renderEmailDetail(){if(!this.email)return;const e=this.container.querySelector("#email-detail-info"),t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#generate-draft-btn"),s=this.container.querySelector("#generate-info-btn"),i=this.container.querySelector(".response-context-selector");t.style.display="none";const r=this.email.sent_date?new Date(this.email.sent_date).toLocaleString():"Unknown",l=new Date(this.email.created_at).toLocaleString(),c=this.email.subject||"(No subject)",o=this.email.from_address||"Unknown sender",u=this.email.plain_text_content||"No content available";e.innerHTML=`
      <div class="email-metadata">
        <div class="metadata-row">
          <label>Subject:</label>
          <span class="email-subject">${this.escapeHtml(c)}</span>
        </div>
        <div class="metadata-row">
          <label>From:</label>
          <span class="email-from">${this.escapeHtml(o)}</span>
        </div>
        <div class="metadata-row">
          <label>To:</label>
          <span class="email-to">${this.email.to_addresses.map(d=>this.escapeHtml(d)).join(", ")}</span>
        </div>
        <div class="metadata-row">
          <label>Sent:</label>
          <span class="email-sent-date">${r}</span>
        </div>
        <div class="metadata-row">
          <label>Processed:</label>
          <span class="email-created-date">${l}</span>
        </div>
        ${this.email.file_path?`
        <div class="metadata-row">
          <label>Source File:</label>
          <span class="email-file-path">${this.escapeHtml(this.email.file_path)}</span>
        </div>
        `:""}
      </div>
      <div class="email-content">
        <h3>Email Content</h3>
        <div class="email-body">
          <pre class="email-text">${this.escapeHtml(u)}</pre>
        </div>
      </div>
    `,e.style.display="block",a.style.display="inline-flex",s.style.display="inline-flex",i.style.display="block"}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}clear(){this.email=null;const e=this.container.querySelector("#email-detail-info"),t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#generate-draft-btn"),s=this.container.querySelector("#generate-info-btn"),i=this.container.querySelector(".response-context-selector");e.style.display="none",t.style.display="none",a.style.display="none",s.style.display="none",i.style.display="none",e.innerHTML=`
      <div class="empty-state">
        <span class="icon">📧</span>
        <h3>Select an email</h3>
        <p>Choose an email from the list to view its details.</p>
      </div>
    `,e.style.display="block"}getCurrentEmail(){return this.email}}class x{container;draftContent="";loading=!1;constructor(e){this.container=e,this.render()}render(){this.container.innerHTML=`
      <div class="draft-display-container">
        <div class="draft-display-header">
          <h2>Generated Draft Reply</h2>
          <div class="draft-display-actions">
            <button id="copy-draft-btn" class="btn btn-secondary" style="display: none;">
              <span class="icon">📋</span>
              Copy to Clipboard
            </button>
            <button id="clear-draft-btn" class="btn btn-outline" style="display: none;">
              <span class="icon">🗑️</span>
              Clear
            </button>
          </div>
        </div>
        <div class="draft-display-content">
          <div id="draft-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Generating draft reply...</p>
          </div>
          <div id="draft-error" class="error-state" style="display: none;"></div>
          <div id="draft-content" class="draft-content-area">
            <div class="empty-state">
              <span class="icon">✨</span>
              <h3>No draft generated</h3>
              <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
            </div>
          </div>
        </div>
      </div>
    `;const e=this.container.querySelector("#copy-draft-btn"),t=this.container.querySelector("#clear-draft-btn");e?.addEventListener("click",()=>this.copyToClipboard()),t?.addEventListener("click",()=>this.clearDraft())}setLoading(e){this.loading=e,this.renderLoadingState()}renderLoadingState(){const e=this.container.querySelector("#draft-loading"),t=this.container.querySelector("#draft-content"),a=this.container.querySelector("#copy-draft-btn"),s=this.container.querySelector("#clear-draft-btn");this.loading?(e.style.display="flex",t.style.display="none",a.style.display="none",s.style.display="none"):(e.style.display="none",t.style.display="block")}renderError(e){const t=this.container.querySelector("#draft-error"),a=this.container.querySelector("#draft-content"),s=this.container.querySelector("#copy-draft-btn"),i=this.container.querySelector("#clear-draft-btn");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="this.retryGeneration()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none",s.style.display="none",i.style.display="none"}displayDraftText(e,t){this.draftContent=e;const a=this.container.querySelector("#draft-content"),s=this.container.querySelector("#draft-error"),i=this.container.querySelector("#copy-draft-btn"),r=this.container.querySelector("#clear-draft-btn");s.style.display="none";const l=t?.contextEmailsCount?`Based on ${t.contextEmailsCount} similar email${t.contextEmailsCount!==1?"s":""}`:"Generated using AI";a.innerHTML=`
      <div class="draft-text-container">
        <div class="draft-metadata">
          <span class="draft-timestamp">Generated: ${new Date().toLocaleString()}</span>
          <span class="draft-length">${e.length} characters</span>
          <span class="draft-context">${l}</span>
        </div>
        <div class="draft-text-area">
          <textarea
            class="draft-textarea"
            readonly
            placeholder="Generated draft will appear here..."
          >${this.escapeHtml(e)}</textarea>
        </div>
        <div class="draft-actions-bottom">
          <button class="btn btn-outline" onclick="this.editDraft()">
            <span class="icon">✏️</span>
            Edit Draft
          </button>
          <button class="btn btn-secondary" onclick="this.regenerateDraft()">
            <span class="icon">🔄</span>
            Regenerate
          </button>
        </div>
      </div>
    `,a.style.display="block",i.style.display="inline-flex",r.style.display="inline-flex"}displayDraft(e){this.draftContent=e.draft;const t=this.container.querySelector("#draft-content"),a=this.container.querySelector("#draft-error"),s=this.container.querySelector("#copy-draft-btn"),i=this.container.querySelector("#clear-draft-btn");a.style.display="none";const r=e.context_emails_count?`Based on ${e.context_emails_count} similar email${e.context_emails_count!==1?"s":""}`:"Generated using AI";let l="";if(e.metadata&&e.metadata.email_weights){const o=e.metadata.email_weights;l=`<div class="weighting-info">
        <span class="weighting-label">Email Weighting Applied:</span>
        <span class="weighting-details">Sent: ${Math.round((o.sent||.66)*100)}%, Inbox: ${Math.round((o.inbox||.34)*100)}%</span>
      </div>`}let c="";e.similar_emails&&e.similar_emails.length>0&&(c=`<div class="similar-emails-info">
        <span class="similar-emails-label">Similar emails found:</span>
        <span class="similar-emails-count">${e.similar_emails.length}</span>
      </div>`),t.innerHTML=`
      <div class="draft-text-container">
        <div class="draft-metadata">
          <span class="draft-timestamp">Generated: ${new Date().toLocaleString()}</span>
          <span class="draft-length">${e.draft.length} characters</span>
          <span class="draft-context">${r}</span>
          ${l}
          ${c}
        </div>
        <div class="draft-text-area">
          <textarea
            class="draft-textarea"
            readonly
            placeholder="Generated draft will appear here..."
          >${this.escapeHtml(e.draft)}</textarea>
        </div>
        <div class="draft-actions-bottom">
          <button class="btn btn-outline" onclick="this.editDraft()">
            <span class="icon">✏️</span>
            Edit Draft
          </button>
          <button class="btn btn-secondary" onclick="this.regenerateDraft()">
            <span class="icon">🔄</span>
            Regenerate
          </button>
        </div>
      </div>
    `,t.style.display="block",s.style.display="inline-flex",i.style.display="inline-flex"}displayError(e){const t=this.container.querySelector("#draft-content"),a=this.container.querySelector("#draft-error"),s=this.container.querySelector("#copy-draft-btn"),i=this.container.querySelector("#clear-draft-btn");a.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <h3>Draft Generation Failed</h3>
        <p>${this.escapeHtml(e)}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,a.style.display="block",t.style.display="none",s.style.display="none",i.style.display="none"}displayPlaceholder(){this.draftContent="";const e=this.container.querySelector("#draft-content"),t=this.container.querySelector("#draft-error"),a=this.container.querySelector("#copy-draft-btn"),s=this.container.querySelector("#clear-draft-btn");t.style.display="none",e.innerHTML=`
      <div class="placeholder-state">
        <span class="icon">🚧</span>
        <h3>Draft Generation Coming Soon</h3>
        <p>The RAG service for AI-powered draft generation is currently being developed.</p>
        <p>For now, you can view email details and prepare for draft generation functionality.</p>
        <div class="placeholder-draft">
          <h4>Sample Draft Preview:</h4>
          <div class="sample-draft">
            <p>Dear [Sender Name],</p>
            <p>Thank you for your email regarding [Subject]. I have reviewed your message and would like to respond as follows:</p>
            <p>[AI-generated response content will appear here]</p>
            <p>Please let me know if you need any additional information.</p>
            <p>Best regards,<br>[Your Name]</p>
          </div>
        </div>
      </div>
    `,e.style.display="block",a.style.display="none",s.style.display="none"}async copyToClipboard(){if(this.draftContent)try{await navigator.clipboard.writeText(this.draftContent),this.showCopySuccess()}catch(e){this.showCopyError(`Failed to copy to clipboard: ${e}`)}}showCopySuccess(){const e=this.container.querySelector("#copy-draft-btn"),t=e.innerHTML;e.innerHTML='<span class="icon">✅</span> Copied!',e.disabled=!0,setTimeout(()=>{e.innerHTML=t,e.disabled=!1},2e3)}showCopyError(e){console.error(e),alert(e)}clearDraft(){this.draftContent="";const e=this.container.querySelector("#draft-content"),t=this.container.querySelector("#copy-draft-btn"),a=this.container.querySelector("#clear-draft-btn");e.innerHTML=`
      <div class="empty-state">
        <span class="icon">✨</span>
        <h3>No draft generated</h3>
        <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
      </div>
    `,t.style.display="none",a.style.display="none"}editDraft(){const e=this.container.querySelector(".draft-textarea");e&&(e.readOnly=!1,e.focus(),e.addEventListener("input",()=>{this.draftContent=e.value}))}regenerateDraft(){this.displayPlaceholder()}retryGeneration(){this.displayPlaceholder()}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}getDraftContent(){return this.draftContent}hasDraft(){return this.draftContent.length>0}}class _{container;currentMode="general";currentContext="general";onDraftGenerated=null;modes=[{id:"general",label:"General Information Request",description:"Ask general questions about your emails and cases (Information Mode)"},{id:"case-specific",label:"Case-Specific Information",description:"Get information about a specific case or legal matter (Information Mode)"},{id:"draft-reply",label:"Draft Email Generator",description:"Generate a draft email response (Draft Mode)"}];contexts=[{id:"general",label:"General Response",description:"Standard professional response"},{id:"lawyer-to-insurance",label:"Lawyer → Insurance",description:"Response from lawyer to insurance company"},{id:"insurance-to-lawyer",label:"Insurance → Lawyer",description:"Response from insurance company to lawyer"}];constructor(e){this.container=e,this.render(),this.setupEventListeners()}setOnDraftGenerated(e){this.onDraftGenerated=e}render(){this.container.innerHTML=`
      <div class="request-interface-container">
        <div class="request-interface-header">
          <h2>AI Request Interface</h2>
          <div class="request-interface-controls">
            <div class="mode-selector">
              <label for="request-mode">Request Mode:</label>
              <select id="request-mode" class="mode-select">
                ${this.modes.map(e=>`
                  <option value="${e.id}" ${e.id===this.currentMode?"selected":""}>
                    ${e.label}
                  </option>
                `).join("")}
              </select>
            </div>
            
            <div class="context-selector">
              <label for="response-context">Response Context:</label>
              <select id="response-context" class="context-select">
                ${this.contexts.map(e=>`
                  <option value="${e.id}" ${e.id===this.currentContext?"selected":""}>
                    ${e.label}
                  </option>
                `).join("")}
              </select>
            </div>
          </div>
        </div>
        
        <div class="request-interface-body">
          <div class="mode-description">
            <p id="mode-description-text">${this.getModeDescription()}</p>
          </div>
          
          <div class="request-input-section">
            <div class="input-group">
              <label for="request-input">Your Request:</label>
              <textarea 
                id="request-input" 
                class="request-textarea" 
                placeholder="${this.getPlaceholderText()}"
                rows="4"
              ></textarea>
            </div>
            
            <div class="request-actions">
              <button id="submit-request" class="btn btn-primary">
                <span class="icon">✨</span>
                ${this.getSubmitButtonText()}
              </button>
              <button id="clear-request" class="btn btn-secondary">
                <span class="icon">🗑️</span>
                Clear
              </button>
            </div>
          </div>
          
          <div id="request-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Processing your request...</p>
          </div>
          
          <div id="request-error" class="error-state" style="display: none;"></div>
        </div>
      </div>
    `}setupEventListeners(){const e=this.container.querySelector("#request-mode"),t=this.container.querySelector("#response-context"),a=this.container.querySelector("#submit-request"),s=this.container.querySelector("#clear-request"),i=this.container.querySelector("#request-input");e?.addEventListener("change",()=>{this.currentMode=e.value,this.updateInterface()}),t?.addEventListener("change",()=>{this.currentContext=t.value,this.updateInterface()}),a?.addEventListener("click",()=>{this.handleSubmitRequest()}),s?.addEventListener("click",()=>{i.value="",this.hideError()}),i?.addEventListener("keydown",r=>{r.ctrlKey&&r.key==="Enter"&&this.handleSubmitRequest()})}updateInterface(){const e=this.container.querySelector("#mode-description-text"),t=this.container.querySelector("#request-input"),a=this.container.querySelector("#submit-request");if(e&&(e.textContent=this.getModeDescription()),t&&(t.placeholder=this.getPlaceholderText()),a){const s=a.childNodes[a.childNodes.length-1];s&&(s.textContent=this.getSubmitButtonText())}}getModeDescription(){return this.modes.find(t=>t.id===this.currentMode)?.description||""}getPlaceholderText(){switch(this.currentMode){case"general":return"Ask any question about your emails, cases, or legal matters...";case"case-specific":return"Enter case details or ask about a specific legal matter...";case"draft-reply":return"Describe the type of reply you want to generate...";default:return"Enter your request..."}}getSubmitButtonText(){switch(this.currentMode){case"general":return"Get Information";case"case-specific":return"Search Case";case"draft-reply":return"Generate Draft";default:return"Submit"}}async handleSubmitRequest(){const e=this.container.querySelector("#request-input"),t=e.value.trim();if(!t){this.showError("Please enter a request before submitting.");return}this.setLoading(!0),this.hideError();try{if(this.currentMode==="draft-reply"){const a=await p("generate_draft",{subject:`${this.currentMode} request`,sender:this.currentContext,content:t,responseContext:this.currentContext});this.onDraftGenerated&&this.onDraftGenerated(a)}else{const a=await p("generate_information",{query:t,context:null,responseContext:this.currentContext}),s={draft:a.information,context_emails_count:a.context_emails_count,similar_emails:a.similar_emails,metadata:{...a.metadata,request_mode:this.currentMode,response_type:"information"}};this.onDraftGenerated&&this.onDraftGenerated(s)}e.value=""}catch(a){console.error("Failed to process request:",a),this.showError(`Failed to process request: ${a}`)}finally{this.setLoading(!1)}}setLoading(e){const t=this.container.querySelector("#request-loading"),a=this.container.querySelector("#submit-request");t&&(t.style.display=e?"block":"none"),a&&(a.disabled=e)}showError(e){const t=this.container.querySelector("#request-error");t&&(t.textContent=e,t.style.display="block")}hideError(){const e=this.container.querySelector("#request-error");e&&(e.style.display="none")}}async function $(n={}){return typeof n=="object"&&Object.freeze(n),await p("plugin:dialog|open",{options:n})}document.querySelector("#app").innerHTML=`
  <div class="app-container">
    <header class="app-header">
      <h1>AI-Assisted Email Response System</h1>
      <div class="header-controls">
        <div class="model-selection">
          <label for="model-select" class="model-label">LLM Model:</label>
          <select id="model-select" class="model-select">
            <option value="">Loading models...</option>
          </select>
          <button id="refresh-models-btn" class="btn btn-secondary btn-small" title="Refresh available models">
            🔄
          </button>
        </div>
        <div class="service-status">
          <div id="service-indicators" class="service-indicators">
            <div class="service-indicator" id="ingestion-status">
              <span class="indicator-dot"></span>
              <span class="indicator-label">Ingestion Service</span>
            </div>
            <div class="service-indicator" id="rag-status">
              <span class="indicator-dot"></span>
              <span class="indicator-label">RAG Service</span>
            </div>
          </div>
          <button id="file-upload-btn" class="btn btn-primary">
            <span class="icon">📁</span>
            Process Email Files
          </button>
        </div>
      </div>
    </header>

    <main class="app-main">
      <div class="app-layout">
        <div class="sidebar">
          <div id="email-list-container" class="email-list-section"></div>
        </div>
        
        <div class="main-content">
          <div class="content-top">
            <div id="email-detail-container" class="email-detail-section"></div>
          </div>

          <div class="content-middle">
            <div id="request-interface-container" class="request-interface-section"></div>
          </div>

          <div class="content-bottom">
            <div id="draft-display-container" class="draft-display-section"></div>
          </div>
        </div>
      </div>
    </main>
  </div>
`;const C=document.querySelector("#email-list-container"),D=document.querySelector("#email-detail-container"),T=document.querySelector("#request-interface-container"),M=document.querySelector("#draft-display-container"),f=new w(C),h=new L(D),I=new _(T),m=new x(M);f.setOnEmailSelect(n=>{h.loadEmail(n),m.displayPlaceholder()});I.setOnDraftGenerated(n=>{m.displayDraft(n)});h.setOnGenerateDraft(async n=>{try{m.setLoading(!0);const e=h.email;if(!e)throw new Error("No email selected");const t=e.subject||"(No subject)",a=e.from_address||"Unknown sender",s=e.plain_text_content||"";if(!s.trim())throw new Error("Email content is empty");const i=h.currentResponseContext||"general",r=await p("generate_draft",{subject:t,sender:a,content:s,responseContext:i});m.displayDraft(r)}catch(e){console.error("Draft generation failed:",e),m.displayError(`Failed to generate draft: ${e}`)}finally{m.setLoading(!1)}});h.setOnGenerateInformation(async(n,e)=>{try{m.setLoading(!0);const t=h.email;if(!t)throw new Error("No email selected");const a=t.subject||"(No subject)",s=t.plain_text_content||"",i=`Provide information about this email: Subject: ${a}`,r=s;if(!s.trim())throw new Error("Email content is empty");const l=await p("generate_information",{query:i,context:r,responseContext:e});m.displayDraftText(l.information,{contextEmailsCount:l.context_emails_count,similarEmails:l.similar_emails,metadata:l.metadata})}catch(t){console.error("Information generation failed:",t),m.displayError(`Failed to generate information: ${t}`)}finally{m.setLoading(!1)}});h.clear();m.displayPlaceholder();const k=document.querySelector("#file-upload-btn");k?.addEventListener("click",async()=>{try{const n=await $({multiple:!0,filters:[{name:"Email Files",extensions:["eml","mbox"]}]});if(n&&Array.isArray(n)){for(const e of n)await b(e);f.refresh()}else n&&(await b(n),f.refresh())}catch(n){console.error("File selection failed:",n),alert(`File selection failed: ${n}`)}});async function b(n){try{const e=n.toLowerCase().endsWith(".mbox")?"mbox":"eml",t=await p("process_email_file",{request:{file_path:n,file_type:e}});console.log("Processing result:",t),alert(`Successfully processed ${n}`)}catch(e){console.error("Processing failed:",e),alert(`Failed to process ${n}: ${e}`)}}async function H(){try{const n=await p("check_service_health",{serviceUrl:"http://localhost:8080"});g("ingestion-status",n),g("rag-status",{service:"http://localhost:8001",status:"unavailable",message:"RAG service not implemented yet"})}catch(n){console.error("Service health check failed:",n)}}function g(n,e){const t=document.querySelector(`#${n}`);if(!t)return;const a=t.querySelector(".indicator-dot");switch(a.classList.remove("status-healthy","status-unhealthy","status-error","status-unavailable"),e.status){case"healthy":a.classList.add("status-healthy");break;case"unhealthy":a.classList.add("status-unhealthy");break;case"unavailable":a.classList.add("status-unavailable");break;default:a.classList.add("status-error")}t.title=`${e.service}: ${e.message}`}async function v(){try{const n=document.querySelector("#model-select");if(!n)return;n.innerHTML='<option value="">Loading models...</option>',n.disabled=!0;const e=await fetch("http://localhost:8003/available_models");if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.json(),a=t.models||[];if(n.innerHTML="",a.length===0){n.innerHTML='<option value="">No models available</option>';return}a.forEach(s=>{const i=document.createElement("option");i.value=s,i.textContent=s,n.appendChild(i)}),t.current_model&&(n.value=t.current_model),n.disabled=!1}catch(n){console.error("Failed to load models:",n);const e=document.querySelector("#model-select");e&&(e.innerHTML='<option value="">Failed to load models</option>',e.disabled=!0)}}async function G(n){try{const e=await fetch("http://localhost:8003/set_model",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:n})});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.json();console.log("Model changed successfully:",t);const a=document.querySelector("#model-select");a&&(a.style.borderColor="#4CAF50",setTimeout(()=>{a.style.borderColor=""},2e3))}catch(e){console.error("Failed to change model:",e),alert(`Failed to change model: ${e}`),v()}}const B=document.querySelector("#model-select"),j=document.querySelector("#refresh-models-btn");B?.addEventListener("change",n=>{const e=n.target;e.value&&G(e.value)});j?.addEventListener("click",()=>{v()});H();v();
