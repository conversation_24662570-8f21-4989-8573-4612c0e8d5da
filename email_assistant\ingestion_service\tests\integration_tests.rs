// Integration tests for the email ingestion service
use ingestion_service::*;
use sqlx::PgPool;
use std::io::Write;
use tempfile::NamedTempFile;
use uuid::Uuid;

// Helper function to create a test database connection
async fn create_test_pool() -> PgPool {
    // Use a test database URL or the same database with a test prefix
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://email_user:email_password@localhost:5432/email_db".to_string());
    
    PgPool::connect(&database_url).await.expect("Failed to connect to test database")
}

// Helper function to clean up test data
async fn cleanup_test_data(pool: &PgPool, message_ids: &[Uuid]) {
    for id in message_ids {
        let _ = sqlx::query("DELETE FROM messages WHERE id = $1")
            .bind(id)
            .execute(pool)
            .await;
    }
}

#[tokio::test]
async fn test_full_pipeline_eml() {
    let pool = create_test_pool().await;
    
    // Create a test .eml file
    let eml_content = "From: <EMAIL>\nSubject: Integration Test\nTo: <EMAIL>\n\nThis is a test email for integration testing.";
    let mut file = NamedTempFile::new().expect("Failed to create temp file");
    file.write_all(eml_content.as_bytes()).expect("Failed to write to temp file");
    let file_path = file.path().to_str().unwrap();

    // Test the full pipeline
    let message_id = ingest_email_file(&pool, file_path).await.expect("Failed to ingest email");
    
    // Verify the message was inserted
    let messages = get_messages(&pool, Some(1)).await.expect("Failed to get messages");
    assert!(!messages.is_empty());
    
    let message = &messages[0];
    assert_eq!(message.id, message_id);
    assert_eq!(message.subject, Some("Integration Test".to_string()));
    assert_eq!(message.from_address, Some("<EMAIL>".to_string()));
    assert_eq!(message.to_addresses, vec!["<EMAIL>".to_string()]);
    assert!(message.cleaned_plain_text_body.is_some());
    
    // Cleanup
    cleanup_test_data(&pool, &[message_id]).await;
}

#[tokio::test]
async fn test_full_pipeline_mbox() {
    let pool = create_test_pool().await;
    
    // Create a test .mbox file with multiple emails
    let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: First Test Email\nTo: <EMAIL>\n\nFirst email body.\n\nFrom <EMAIL> Mon Jan  1 11:00:00 2024\nFrom: <EMAIL>\nSubject: Second Test Email\nTo: <EMAIL>\n\nSecond email body.\n\n";
    let mut file = NamedTempFile::new().expect("Failed to create temp file");
    file.write_all(mbox_content.as_bytes()).expect("Failed to write to temp file");
    let file_path = file.path().to_str().unwrap();

    // Test the full pipeline
    let message_ids = ingest_mbox_file(&pool, file_path).await.expect("Failed to ingest mbox");
    assert_eq!(message_ids.len(), 2);
    
    // Verify the messages were inserted
    let messages = get_messages(&pool, Some(10)).await.expect("Failed to get messages");
    let test_messages: Vec<_> = messages.into_iter()
        .filter(|m| message_ids.contains(&m.id))
        .collect();
    
    assert_eq!(test_messages.len(), 2);
    
    // Check first message
    let first_msg = test_messages.iter().find(|m| m.subject == Some("First Test Email".to_string())).unwrap();
    assert_eq!(first_msg.from_address, Some("<EMAIL>".to_string()));
    
    // Check second message
    let second_msg = test_messages.iter().find(|m| m.subject == Some("Second Test Email".to_string())).unwrap();
    assert_eq!(second_msg.from_address, Some("<EMAIL>".to_string()));
    
    // Cleanup
    cleanup_test_data(&pool, &message_ids).await;
}

#[tokio::test]
async fn test_database_connection_and_migrations() {
    let pool = establish_connection().await.expect("Failed to establish connection");
    
    // Test that we can run migrations without error
    run_migrations(&pool).await.expect("Failed to run migrations");
    
    // Test that we can query the database
    let messages = get_messages(&pool, Some(1)).await.expect("Failed to query messages");
    // Should not fail even if no messages exist
    assert!(messages.len() <= 1);
}

#[tokio::test]
async fn test_message_insertion_and_retrieval() {
    let pool = create_test_pool().await;
    
    // Create a test message
    let parsed_email = ParsedEmail {
        id: Uuid::new_v4(),
        subject: Some("Test Subject".to_string()),
        from: Some("<EMAIL>".to_string()),
        to: vec!["<EMAIL>".to_string()],
        sent_date: None,
        plain_text_body_raw: Some("Test body".to_string()),
        html_body_raw: None,
        cleaned_plain_text_body: Some("Test body".to_string()),
    };
    
    let message = Message::from(parsed_email);
    let message_id = message.id;
    
    // Insert the message
    let inserted_id = insert_message(&pool, &message).await.expect("Failed to insert message");
    assert_eq!(inserted_id, message_id);
    
    // Retrieve and verify
    let messages = get_messages(&pool, Some(10)).await.expect("Failed to get messages");
    let test_message = messages.into_iter().find(|m| m.id == message_id);
    
    assert!(test_message.is_some());
    let test_message = test_message.unwrap();
    assert_eq!(test_message.subject, Some("Test Subject".to_string()));
    assert_eq!(test_message.from_address, Some("<EMAIL>".to_string()));
    
    // Cleanup
    cleanup_test_data(&pool, &[message_id]).await;
}

#[tokio::test]
async fn test_error_handling() {
    let pool = create_test_pool().await;
    
    // Test with non-existent file
    let result = ingest_email_file(&pool, "non_existent_file.eml").await;
    assert!(result.is_err());
    
    // Test with invalid email content
    let invalid_content = "This is not a valid email";
    let mut file = NamedTempFile::new().expect("Failed to create temp file");
    file.write_all(invalid_content.as_bytes()).expect("Failed to write to temp file");
    let file_path = file.path().to_str().unwrap();
    
    // Should still succeed but with minimal data
    let result = ingest_email_file(&pool, file_path).await;
    if let Ok(message_id) = result {
        cleanup_test_data(&pool, &[message_id]).await;
    }
}
