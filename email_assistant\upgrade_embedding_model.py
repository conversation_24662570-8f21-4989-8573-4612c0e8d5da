#!/usr/bin/env python3
"""
Embedding Model Upgrade Script
Upgrades from all-MiniLM-L6-v2 (384d) to e5-large-v2 (1024d)

This script:
1. Creates a new Qdrant collection with 1024 dimensions
2. Backs up the old collection
3. Provides instructions for re-indexing emails
"""

import asyncio
import os
import sys
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, CreateCollection

async def main():
    print("🔄 Embedding Model Upgrade: all-MiniLM-L6-v2 → e5-large-v2")
    print("=" * 60)
    
    # Connect to Qdrant
    qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
    print(f"Connecting to Qdrant at {qdrant_url}...")
    
    try:
        client = QdrantClient(url=qdrant_url)
        
        # Check if old collection exists
        old_collection = "emails"
        new_collection = "emails_e5_large_v2"
        backup_collection = "emails_backup_minilm"
        
        print(f"\n📊 Checking existing collections...")
        
        try:
            old_info = client.get_collection(old_collection)
            print(f"✓ Found existing collection '{old_collection}' with {old_info.vectors_count} vectors")
            print(f"  Dimension: {old_info.config.params.vectors.size}")
            
            # Create backup
            print(f"\n💾 Creating backup collection '{backup_collection}'...")
            try:
                client.create_collection(
                    collection_name=backup_collection,
                    vectors_config=VectorParams(
                        size=384,  # Original dimension
                        distance=Distance.COSINE
                    )
                )
                print(f"✓ Backup collection '{backup_collection}' created")
                
                # Note: In a production system, you'd copy the data here
                print("⚠️  Note: Data copying not implemented in this script")
                print("   Your original data is safe in the 'emails' collection")
                
            except Exception as e:
                if "already exists" in str(e):
                    print(f"✓ Backup collection '{backup_collection}' already exists")
                else:
                    print(f"❌ Error creating backup: {e}")
                    
        except Exception as e:
            print(f"ℹ️  No existing collection found: {e}")
        
        # Create new collection with e5-large-v2 dimensions
        print(f"\n🆕 Creating new collection '{new_collection}' for e5-large-v2...")
        try:
            client.create_collection(
                collection_name=new_collection,
                vectors_config=VectorParams(
                    size=1024,  # e5-large-v2 dimension
                    distance=Distance.COSINE
                )
            )
            print(f"✓ New collection '{new_collection}' created successfully")
            
        except Exception as e:
            if "already exists" in str(e):
                print(f"✓ Collection '{new_collection}' already exists")
            else:
                print(f"❌ Error creating new collection: {e}")
                return
        
        # Rename collections for the upgrade
        print(f"\n🔄 Preparing for model upgrade...")
        
        try:
            # Delete the old emails collection to make room for the new one
            print(f"⚠️  Deleting old 'emails' collection...")
            print("   (Your data is backed up in 'emails_backup_minilm')")
            
            response = input("Continue? (y/N): ")
            if response.lower() != 'y':
                print("❌ Upgrade cancelled")
                return
                
            client.delete_collection(old_collection)
            print(f"✓ Old collection deleted")
            
            # Rename new collection to 'emails'
            print(f"🔄 Renaming '{new_collection}' to 'emails'...")
            # Note: Qdrant doesn't have a rename operation, so we'll use the new collection name
            # and update our code to use it, or recreate with the right name
            
            client.create_collection(
                collection_name="emails",
                vectors_config=VectorParams(
                    size=1024,  # e5-large-v2 dimension
                    distance=Distance.COSINE
                )
            )
            print(f"✓ New 'emails' collection created with 1024 dimensions")
            
            # Clean up temporary collection
            try:
                client.delete_collection(new_collection)
                print(f"✓ Temporary collection cleaned up")
            except:
                pass
                
        except Exception as e:
            print(f"❌ Error during collection upgrade: {e}")
            return
        
        print(f"\n✅ Embedding model upgrade completed!")
        print(f"📋 Next steps:")
        print(f"   1. Restart the RAG service to load e5-large-v2")
        print(f"   2. Re-index your emails with the new embedding model")
        print(f"   3. Test the improved email similarity search")
        print(f"\n💡 To re-index emails, use the ingestion service to process your email files again")
        print(f"   The new e5-large-v2 model will generate better embeddings!")
        
    except Exception as e:
        print(f"❌ Error connecting to Qdrant: {e}")
        print(f"   Make sure Qdrant is running at {qdrant_url}")
        return

if __name__ == "__main__":
    asyncio.run(main())
