# AI‑Assisted Email Response System – Project Blueprint

## One‑Paragraph Summary

Build a retrieval‑augmented‑generation (RAG) assistant that ingests \~200 000 legal‑advisor emails from IMAP/Thunderbird, cleans and embeds them, stores both metadata (PostgreSQL) and vectors (pgvector/Qdrant/Milvus), then exposes an API that drafts client replies with GPT‑4o or a local Llama‑3 model while satisfying GDPR & EU‑AI‑Act constraints.  Deployment is containerised and extensible (Python or Rust workers), with feedback‑driven continuous improvement.

---

## 1  Extraction & Ingestion

| Task                  | Python‑first option                   | Rust‑first option                   | Key Points                          |
| --------------------- | ------------------------------------- | ----------------------------------- | ----------------------------------- |
| Fetch from IMAP       | `imapclient` (TLS, IDLE, incremental) | `mail-parser` crate after raw fetch | Avoid raw `imaplib`; faster & safer |
| Read Thunderbird mbox | `mailbox.mbox` or                     |                                     |                                     |

|   |
| - |

|   |
| - |

| **Apache Tika MboxParser** | Same Tika via JNI / CLI          | Streams GB‑sized mbox without full load |                                  |
| -------------------------- | -------------------------------- | --------------------------------------- | -------------------------------- |
| Pipeline orchestration     | **LlamaIndex IngestionPipeline** | – (call via Python micro‑service)       | Declarative transforms & caching |

> **Tip – Signature & Quote Stripping:** Use `mailgun/talon` to drop disclaimers, signatures and quoted replies before embedding.

---

## 2  Normalisation & Enrichment

1. Convert HTML → plain‑text while preserving lists/tables.
2. Thread detection (RFC 5256) so prompts can include conversation context.
3. Auto‑label: matter type, urgency, jurisdiction.  Start zero‑shot; fine‑tune after ≈ 1 000 hand‑labels.

---

## 3  Storage Layer

| Layer           | Technology                                                               | Notes                                                                                      |
| --------------- | ------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------ |
| Relational      | **PostgreSQL 16**                                                        | `messages` table: `id`, `thread_id`, `subject`, `from`, `to`, `date`, `path_raw_eml`, etc. |
| Vector          | **Option A:** pgvector extension**Option B:** Qdrant / Milvus 3 / Chroma | Handles >10 M dense vectors; hybrid BM25 + semantic search                                 |
| Metadata search | Postgres `tsvector` or OpenSearch                                        | Keep legal‑discovery happy                                                                 |

---

## 4  Embeddings & Classification

- **Embedding Models**

  - *Cloud:* `text-embedding-3-large` (OpenAI) – best multilingual legal recall; cost ≈ \$0.13 per 1 000 emails.
  - *Local:* `multilingual‑e5‑large` or `bge‑m3` via Sentence‑Transformers for air‑gapped needs.

- **Batching:** Embed 8‑16 k tokens per call for throughput & rate‑limit safety.

- **Primary categorisation:** cosine nearest‑neighbour; fall back to LLM zero‑shot on low‑confidence clusters.

---

## 5  Draft‑Generation Pipeline (RAG)

| Step                | Framework                                            | Purpose                       |
| ------------------- | ---------------------------------------------------- | ----------------------------- |
| Retrieve top‑k docs | LangChain / LlamaIndex retriever                     | hybrid similarity + BM25      |
| Prompt composition  | LangChain `PromptTemplate`                           | question + exemplars + policy |
| LLM generation      | GPT‑4o / GPT‑4o‑mini (128 k ctx) or local Llama‑3 8B | choose per privacy / cost     |
| Return sources      | Built‑in `return_source_documents`                   | transparency & audit          |

> **Fine‑tuning:** Export ⟨question, draft⟩ pairs and fine‑tune GPT‑4o‑mini or apply LoRA on Llama‑3 8B.  5 k–10 k curated pairs markedly beat zero‑shot.

---

## 6  Governance & Compliance

- **GDPR:** Pseudonymise personal data before cloud calls.
- **EU AI Act:** Human‑in‑the‑loop email drafting is “limited‑risk”; automatic sending becomes **high‑risk** → keep output logs ≥ 6 months, run bias/safety evaluations (e.g. Llama‑Guard 3) and produce risk assessment reports.

---

## 7  Deployment Sketch

```ascii
┌────────────────────────────────────────────────────────┐
│ 1. Ingestion Cron (Python)                            │
│    ├─ fetch new IMAP UIDs → mbox → S3                 │
│    └─ publish job to RabbitMQ                         │
└────────────────────────────────────────────────────────┘
            │
            ▼
┌────────────────────────────────────────────────────────┐
│ 2. Worker Pool (Rust or Python async)                 │
│    ├─ parse → clean → embed                           │
│    ├─ insert metadata rows in Postgres                │
│    └─ upsert vectors in pgvector / Qdrant / Milvus    │
└────────────────────────────────────────────────────────┘
            │
            ▼
┌────────────────────────────────────────────────────────┐
│ 3. Draft API (FastAPI / Actix)                        │
│    ├─ receive client email                            │
│    ├─ retrieve similar vectors                        │
│    ├─ call LLM with RAG prompt                        │
│    └─ return JSON {draft, citations, scores}          │
└────────────────────────────────────────────────────────┘
```

Use Docker Compose or Kubernetes.  GPU‑heavy services (embedding, local Llama) get A100/H100 nodes; others run on CPU.

---

## 8  Minimal Prototype Checklist

1. Spin up Postgres + pgvector locally.
2. Python script with `imapclient` to pull 5 000 mails → `.mbox`.
3. Parse mbox with Apache Tika → raw `.eml` files.
4. Strip signatures using Talon, embed with `multilingual‑e5‑large`.
5. Load vectors into pgvector and query cosine.
6. Wrap in LangChain RetrievalQA with GPT‑4o‑mini.
7. Manually review 50 drafts, refine prompt, iterate.

---

## 9  Key References

1. IMAPClient docs – robust IMAP fetching
2. Apache Tika MboxParser – stream large mbox
3. Talon signature extractor – reliable disclaimer stripping
4. pgvector – vectors inside Postgres
5. Qdrant & Milvus – standalone vector DBs
6. multilingual‑e5‑large & bge‑m3 – local embedding models
7. LangChain RAG tutorial – retrieval QA examples
8. EU AI Act enforcement timeline – compliance context

(*URLs and full citations omitted in this markdown export; see chat history for detailed sources.*)

---

## 10  Prompts for Downstream AI Agents

Below are ready‑to‑use prompts you can feed to ChatGPT/LLM to generate more artefacts from this blueprint.

### 10.1  Functional Specification Draft

> **Prompt:** “You are a senior software architect.  Using the *AI‑Assisted Email Response System – Project Blueprint* below, draft a full functional specification covering system goals, user stories, non‑functional requirements, data model, security posture and compliance obligations.”

### 10.2  Technical Design Document

> **Prompt:** “Act as a principal engineer.  Produce a detailed technical design (sequence diagrams, component responsibilities, API contracts, scaling considerations) implementing the architecture described in the blueprint.”

### 10.3  Project Plan & To‑Do List

> **Prompt:** “You are an engineering manager.  Break the implementation of the blueprint into epics, tasks and sub‑tasks with rough time estimates (in person‑days) and assign tentative roles (backend, ML engineer, DevOps, legal).  Output as a markdown checklist.”

### 10.4  Risk Register

> **Prompt:** “As a compliance officer, create a risk register enumerating privacy, security, ethical and project‑management risks inherent in the blueprint, including mitigation strategies and monitoring KPIs.”

### 10.5  Test Plan

> **Prompt:** “You are a QA lead.  Compose a comprehensive test plan (unit, integration, load, security, compliance) for the system described in the blueprint.”

Feel free to replace model names, hosting constraints or compliance frameworks in any of these prompts to match your environment.

