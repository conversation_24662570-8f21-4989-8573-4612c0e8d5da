// Thunderbird Email Processor CLI
// Command-line utility for processing Thunderbird email files

use std::env;
use std::process;
use ingestion_service::thunderbird_processor::{ThunderbirdProcessor, ProcessingMode};

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() < 3 || args.len() > 4 {
        eprintln!("Usage: {} <thunderbird_directory> <output_mbox_file> [processing_mode]", args[0]);
        eprintln!();
        eprintln!("Arguments:");
        eprintln!("  thunderbird_directory  Path to Thunderbird profile directory containing .sbd folders");
        eprintln!("  output_mbox_file      Path for the enhanced mbox output file");
        eprintln!("  processing_mode       Optional: single-pass, two-pass, or year-based (default: single-pass)");
        eprintln!();
        eprintln!("Processing Modes:");
        eprintln!("  single-pass   Fast, memory-efficient, may miss some cross-file threading");
        eprintln!("  two-pass      Memory-efficient with perfect threading (recommended)");
        eprintln!("  year-based    Process in yearly batches (for very large datasets)");
        eprintln!();
        eprintln!("Examples:");
        eprintln!("  {} /path/to/thunderbird/profile/Mail/Local\\ Folders processed_emails.mbox", args[0]);
        eprintln!("  {} /path/to/thunderbird/profile/Mail/Local\\ Folders processed_emails.mbox two-pass", args[0]);
        process::exit(1);
    }
    
    let thunderbird_dir = &args[1];
    let output_file = &args[2];

    // Parse processing mode
    let processing_mode = if args.len() >= 4 {
        match args[3].as_str() {
            "single-pass" => ProcessingMode::SinglePass,
            "two-pass" => ProcessingMode::TwoPass,
            "year-based" => ProcessingMode::YearBased { batch_size_years: 1 },
            _ => {
                eprintln!("Invalid processing mode: {}. Using single-pass.", args[3]);
                ProcessingMode::SinglePass
            }
        }
    } else {
        ProcessingMode::SinglePass
    };

    println!("Thunderbird Email Processor");
    println!("===========================");
    println!("Input directory: {}", thunderbird_dir);
    println!("Output file: {}", output_file);
    println!("Processing mode: {:?}", processing_mode);
    println!();

    let mut processor = ThunderbirdProcessor::with_mode(processing_mode);
    
    match processor.process_thunderbird_directory(thunderbird_dir, output_file) {
        Ok(report) => {
            report.print_summary();
            println!("\nProcessing completed successfully!");
        }
        Err(e) => {
            eprintln!("Error during processing: {}", e);
            process::exit(1);
        }
    }
}
