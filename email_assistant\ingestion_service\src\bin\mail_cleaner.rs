// Mail Cleaner Utility - Removes S/MIME signatures and binary content from mbox files
// Enhanced with DOC/DOCX text extraction
// Usage: mail_cleaner.exe <input_file> <output_file>

use std::env;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ufWriter, Read, Write, Result};
use std::collections::HashMap;
use html2text;

#[derive(Debug, Default)]
struct DebugStats {
    total_lines: usize,
    header_lines: usize,
    body_lines: usize,
    binary_lines_skipped: usize,
    html_sections_found: usize,
    doc_attachments_found: usize,
    lines_kept: usize,
    lines_filtered: usize,
}

impl DebugStats {
    fn new() -> Self {
        Self::default()
    }
}

fn main() -> Result<()> {
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        eprintln!("Usage: {} <input_file_or_directory> <output_file> [folder_type]", args[0]);
        eprintln!("Examples:");
        eprintln!("  {} f:\\Inbox f:\\Inbox_cleaned.mbox", args[0]);
        eprintln!("  {} f:\\Sent f:\\Sent_cleaned.mbox Sent", args[0]);
        eprintln!("  {} f:\\2021.sbd\\ f:\\2021_all_cleaned.mbox", args[0]);
        std::process::exit(1);
    }

    let input_path = &args[1];
    let output_path = &args[2];
    let explicit_folder_type = args.get(3).map(|s| s.as_str());

    println!("=== MAIL CLEANER DEBUG MODE ===");
    println!("Input:  {}", input_path);
    println!("Output: {}", output_path);
    println!("Explicit folder type: {:?}", explicit_folder_type);

    let start_time = std::time::Instant::now();

    // Check if input is a directory or single file
    let input_metadata = std::fs::metadata(input_path)?;
    if input_metadata.is_dir() {
        println!("Processing directory: {}", input_path);
        process_directory(input_path, output_path)
    } else {
        println!("Input file size: {:.1} MB", input_metadata.len() as f64 / (1024.0 * 1024.0));

        // Detect folder type
        let folder_type = explicit_folder_type.unwrap_or_else(|| detect_folder_type(input_path));
        println!("Detected folder type: {}", folder_type);

        // Process single file
        let (emails_processed, emails_cleaned) = clean_mbox_file(input_path, output_path, folder_type)?;

        let duration = start_time.elapsed();

        // Get output file size
        let output_metadata = std::fs::metadata(output_path)?;
        let output_size_mb = output_metadata.len() as f64 / (1024.0 * 1024.0);
        let input_size_mb = input_metadata.len() as f64 / (1024.0 * 1024.0);
        let compression_ratio = if output_size_mb > 0.0 { input_size_mb / output_size_mb } else { 0.0 };

        println!("\n=== Cleaning Complete ===");
        println!("Emails processed: {}", emails_processed);
        println!("Emails cleaned: {}", emails_cleaned);
        println!("Output file size: {:.1} MB", output_size_mb);
        println!("Compression ratio: {:.1}:1", compression_ratio);
        println!("Processing time: {:.2}s", duration.as_secs_f64());
        println!("Cleaned file: {}", output_path);

        Ok(())
    }
}

/// Detect folder type from file path
fn detect_folder_type(file_path: &str) -> &'static str {
    let path_lower = file_path.to_lowercase();

    if path_lower.contains("inbox") {
        "Inbox"
    } else if path_lower.contains("sent") {
        "Sent"
    } else if path_lower.contains("draft") {
        "Drafts"
    } else if path_lower.contains("trash") || path_lower.contains("deleted") {
        "Trash"
    } else if path_lower.contains("junk") || path_lower.contains("spam") {
        "Junk"
    } else {
        "Inbox" // Default to Inbox if unknown
    }
}

/// Process a directory containing multiple mbox files
fn process_directory(input_dir: &str, output_path: &str) -> Result<()> {
    use std::path::Path;
    use std::fs;

    let output_file = File::create(output_path)?;
    let mut writer = BufWriter::new(output_file);

    let mut total_emails = 0;
    let mut total_cleaned = 0;
    let start_time = std::time::Instant::now();

    // Find all mbox files in directory
    let dir_path = Path::new(input_dir);
    let entries = fs::read_dir(dir_path)?;

    for entry in entries {
        let entry = entry?;
        let file_path = entry.path();

        if file_path.is_file() {
            let file_path_str = file_path.to_string_lossy();

            // Skip files with extensions (except .mbox)
            if let Some(ext) = file_path.extension() {
                if ext != "mbox" {
                    continue;
                }
            }

            // Detect folder type for this file
            let folder_type = detect_folder_type(&file_path_str);
            println!("Processing {} ({})", file_path_str, folder_type);

            // Process this file and append to output
            let (emails, cleaned) = process_single_file_to_writer(&file_path_str, &mut writer, folder_type)?;
            total_emails += emails;
            total_cleaned += cleaned;
        }
    }

    writer.flush()?;
    let duration = start_time.elapsed();

    println!("\n=== Directory Processing Complete ===");
    println!("Total emails processed: {}", total_emails);
    println!("Total emails cleaned: {}", total_cleaned);
    println!("Processing time: {:.2}s", duration.as_secs_f64());
    println!("Combined output: {}", output_path);

    Ok(())
}

fn clean_mbox_file(input_path: &str, output_path: &str, folder_type: &str) -> Result<(usize, usize)> {
    let output_file = File::create(output_path)?;
    let mut writer = BufWriter::new(output_file);

    let result = process_single_file_to_writer(input_path, &mut writer, folder_type)?;
    writer.flush()?;
    Ok(result)
}

fn process_single_file_to_writer(input_path: &str, writer: &mut BufWriter<File>, folder_type: &str) -> Result<(usize, usize)> {
    println!("  DEBUG: Opening file: {}", input_path);
    let input_file = File::open(input_path)?;
    let file_size = input_file.metadata()?.len();
    println!("  DEBUG: File size: {:.2} MB", file_size as f64 / (1024.0 * 1024.0));

    let mut reader = BufReader::new(input_file);

    let mut buffer = [0; 16384]; // Larger buffer for better performance
    let mut leftover_bytes = Vec::new();
    let mut emails_processed = 0;
    let mut emails_cleaned = 0;
    let mut bytes_read_total = 0u64;

    // Streaming state
    let mut in_email = false;
    let mut in_headers = true;
    let mut skip_binary_section = false;
    let mut current_boundary: Option<String> = None;
    let mut lines_skipped_in_current_email = 0;

    // Document extraction state - use streaming approach to avoid memory issues
    let mut current_email_lines = Vec::new();
    let mut document_text_extracted = false;
    let mut current_email_size = 0usize;

    loop {
        let bytes_read = reader.read(&mut buffer)?;
        if bytes_read == 0 {
            break; // End of file
        }

        bytes_read_total += bytes_read as u64;

        // Progress indicator for large files
        if bytes_read_total % (100 * 1024 * 1024) == 0 { // Every 100MB
            let progress_pct = (bytes_read_total as f64 / file_size as f64) * 100.0;
            println!("  DEBUG: Read {:.1}% of file ({:.1} MB / {:.1} MB)",
                     progress_pct,
                     bytes_read_total as f64 / (1024.0 * 1024.0),
                     file_size as f64 / (1024.0 * 1024.0));
        }

        // Combine leftover bytes from previous iteration
        let mut all_bytes = leftover_bytes.clone();
        all_bytes.extend_from_slice(&buffer[..bytes_read]);
        leftover_bytes.clear();

        let mut line_start = 0;
        for i in 0..all_bytes.len() {
            if all_bytes[i] == b'\n' || i == all_bytes.len() - 1 {
                let line_end = if i == all_bytes.len() - 1 && bytes_read < buffer.len() {
                    i + 1
                } else if all_bytes[i] == b'\n' {
                    i
                } else {
                    // Incomplete line, save for next iteration
                    leftover_bytes.extend_from_slice(&all_bytes[line_start..]);
                    break;
                };

                let line_bytes = &all_bytes[line_start..line_end];
                let line_str = String::from_utf8_lossy(line_bytes);
                let line_str = line_str.trim_end_matches('\r');

                // Check for mbox separator lines
                // Support both standard mbox format (From <EMAIL>) and Thunderbird format (From )
                if line_str.starts_with("From ") || line_str.starts_with("From - ") {

                    // Finalize previous email
                    if in_email {
                        emails_processed += 1;

                        // Process the completed email: extract documents and write cleaned version
                        if !current_email_lines.is_empty() {
                            let (cleaned_email, has_docs) = process_email_lines_with_document_extraction(&current_email_lines, folder_type);
                            writer.write_all(cleaned_email.as_bytes())?;

                            if has_docs {
                                emails_cleaned += 1;
                                document_text_extracted = true;
                            } else if lines_skipped_in_current_email > 0 {
                                emails_cleaned += 1;
                            }
                        }

                        // Progress indicator - more frequent for debugging
                        if emails_processed % 100 == 0 {
                            let doc_status = if document_text_extracted { " (with docs)" } else { "" };
                            println!("  Processed {} emails{} ...", emails_processed, doc_status);
                        }

                        // Debug: Show first few emails processed and memory usage
                        if emails_processed <= 10 {
                            println!("  DEBUG: Processed email #{} (email size: {:.1} KB)", emails_processed, current_email_size as f64 / 1024.0);
                        }

                        // Debug: Warn about very large emails
                        if current_email_size > 10_000_000 { // > 10MB
                            println!("  DEBUG: Large email #{} processed ({:.1} MB)", emails_processed, current_email_size as f64 / (1024.0 * 1024.0));
                        }
                    }

                    // Start new email
                    in_email = true;
                    in_headers = true;
                    skip_binary_section = false;
                    current_boundary = None;
                    lines_skipped_in_current_email = 0;

                    // Reset document extraction state
                    current_email_lines.clear();
                    current_email_size = 0;
                    document_text_extracted = false;

                    // Start collecting email content for document extraction
                    current_email_lines.push(line_str.to_string());
                    current_email_size += line_str.len() + 1; // +1 for newline
                } else if in_email {
                    // Collect email content for document extraction, but limit memory usage
                    if current_email_size < 10_000_000 { // Limit to 10MB per email to prevent memory issues
                        current_email_lines.push(line_str.to_string());
                        current_email_size += line_str.len() + 1;
                    }
                    // Skip collecting more content for large emails to prevent memory issues
                }

                line_start = i + 1;
            }
        }

        // Save incomplete line for next iteration
        if line_start < all_bytes.len() {
            leftover_bytes.extend_from_slice(&all_bytes[line_start..]);
        }
    }

    // Finalize last email
    if in_email {
        emails_processed += 1;

        // Process the final email: extract documents and write cleaned version
        if !current_email_lines.is_empty() {
            let (cleaned_email, has_docs) = process_email_lines_with_document_extraction(&current_email_lines, folder_type);
            writer.write_all(cleaned_email.as_bytes())?;

            if has_docs {
                emails_cleaned += 1;
            } else if lines_skipped_in_current_email > 0 {
                emails_cleaned += 1;
            }
        }
    }

    writer.flush()?;
    Ok((emails_processed, emails_cleaned))
}

/// Process a single email line and determine if it should be kept
/// Returns true if the line should be written to output, false if it should be skipped
fn process_email_line(
    line: &str,
    in_headers: &mut bool,
    skip_binary_section: &mut bool,
    current_boundary: &mut Option<String>
) -> bool {
    if *in_headers {
        // Keep all headers but extract boundary info
        if line.trim().is_empty() {
            *in_headers = false;
        } else if line.to_lowercase().contains("boundary=") {
            // Extract boundary for MIME part detection
            if let Some(start) = line.find("boundary=") {
                let boundary_part = &line[start + 9..];
                *current_boundary = Some(boundary_part.trim_matches('"').trim_matches('\'').to_string());
            }
        }
        return true; // Keep all header lines
    }

    // Body processing - aggressively filter binary content

    // Reset skip flag on MIME boundaries
    if let Some(boundary) = current_boundary.as_ref() {
        if line.contains(boundary) {
            *skip_binary_section = false;
            return true;
        }
    }

    // Also reset on any boundary-like line
    if line.starts_with("--") && line.len() > 10 {
        *skip_binary_section = false;
        return true;
    }

    // Skip S/MIME signature sections
    if line.contains("Content-Type: application/pkcs7-signature") ||
       line.contains("Content-Type: application/x-pkcs7-signature") ||
       line.contains("smime.p7s") {
        *skip_binary_section = true;
        return false;
    }

    // Skip base64 transfer encoding sections
    if line.to_lowercase().contains("content-transfer-encoding: base64") {
        *skip_binary_section = true;
        return false;
    }

    // Skip attachment sections
    if line.to_lowercase().contains("content-disposition: attachment") {
        *skip_binary_section = true;
        return false;
    }

    // Skip if we're in a binary section
    if *skip_binary_section {
        // Skip base64 data lines (long lines with only base64 characters)
        if line.len() > 40 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
            return false;
        }
    }

    // Keep the line if it's not binary content
    true
}

/// Process email lines: extract documents, clean HTML, and return cleaned email content
fn process_email_lines_with_document_extraction(email_lines: &[String], folder_type: &str) -> (String, bool) {
    // Convert to the format expected by the rest of the function
    let lines: Vec<&str> = email_lines.iter().map(|s| s.as_str()).collect();
    let mut cleaned_lines = Vec::new();
    let mut extracted_docs: Vec<String> = Vec::new();
    let mut extracted_html_text: Vec<String> = Vec::new();

    let mut in_headers = true;
    let mut skip_binary_section = false;
    let mut current_boundary = None;
    let mut is_mbox_separator = true; // First line is mbox separator

    // Debug: Track what we're processing
    let mut debug_stats = DebugStats::new();
    debug_stats.total_lines = lines.len();

    // First pass: extract documents and clean content
    for line in &lines {
        if is_mbox_separator {
            // Handle mbox separator
            cleaned_lines.push(line.to_string());
            cleaned_lines.push(format!("X-Folder-Type: {}", folder_type));
            is_mbox_separator = false;
            continue;
        }

        if in_headers {
            debug_stats.header_lines += 1;
            // Keep all headers but extract boundary info
            if line.trim().is_empty() {
                in_headers = false;
            } else if line.to_lowercase().contains("boundary=") {
                if let Some(start) = line.find("boundary=") {
                    let boundary_part = &line[start + 9..];
                    current_boundary = Some(boundary_part.trim_matches('"').trim_matches('\'').to_string());
                }
            }
            cleaned_lines.push(line.to_string());
            debug_stats.lines_kept += 1;
            continue;
        }

        debug_stats.body_lines += 1;

        // Body processing with basic binary filtering (without complex document extraction)

        // Check if we should start skipping binary content
        if is_binary_attachment_or_content(line) {
            skip_binary_section = true;
        }

        // Check if we should stop skipping (MIME boundary or empty line)
        if skip_binary_section && (line.starts_with("--") || line.trim().is_empty()) {
            skip_binary_section = false;
        }

        let should_keep = !is_binary_attachment_or_content(line) &&
                         !is_encoded_content_line(line) &&
                         !skip_binary_section;

        if should_keep {
            cleaned_lines.push(line.to_string());
            debug_stats.lines_kept += 1;
        } else {
            debug_stats.lines_filtered += 1;
        }
    }

    // Return true if we filtered out any content (indicating cleaning happened)
    let has_cleaned_content = debug_stats.lines_filtered > 0;

    (cleaned_lines.join("\n"), has_cleaned_content)
}

/// Legacy function for backward compatibility - converts string to lines and calls the new function
fn process_email_with_document_extraction(email_content: &str, folder_type: &str) -> (String, bool) {
    let lines: Vec<String> = email_content.lines().map(|s| s.to_string()).collect();
    process_email_lines_with_document_extraction(&lines, folder_type)
}

/// Enhanced line processing that extracts documents and HTML content before filtering
fn process_email_line_with_content_extraction(
    line: &str,
    skip_binary_section: &mut bool,
    current_boundary: &mut Option<String>,
    extracted_docs: &mut Vec<String>,
    extracted_html_text: &mut Vec<String>,
    full_email_content: &str,
    debug_stats: &mut DebugStats
) -> bool {
    // Reset skip flag on MIME boundaries
    if let Some(boundary) = current_boundary.as_ref() {
        if line.contains(boundary) {
            *skip_binary_section = false;
            return true;
        }
    }

    // Also reset on any boundary-like line
    if line.starts_with("--") && line.len() > 10 {
        *skip_binary_section = false;
        return true;
    }

    // Check for document attachments before skipping
    if line.to_lowercase().contains("content-disposition:") && line.to_lowercase().contains("attachment") {
        debug_stats.doc_attachments_found += 1;
        // Try to extract document from this attachment
        if let Some(doc_text) = extract_document_from_attachment_section(full_email_content, line) {
            extracted_docs.push(doc_text);
        }
    }

    // Check for HTML content sections
    if line.to_lowercase().contains("content-type: text/html") {
        debug_stats.html_sections_found += 1;
        // Try to extract and convert HTML to text
        if let Some(html_text) = extract_html_content_from_email(full_email_content) {
            extracted_html_text.push(html_text);
        } else {
            println!("    DEBUG: Failed to extract HTML content");
        }
    }

    // Skip S/MIME signature sections
    if line.contains("Content-Type: application/pkcs7-signature") ||
       line.contains("Content-Type: application/x-pkcs7-signature") ||
       line.contains("smime.p7s") {
        *skip_binary_section = true;
        return false;
    }

    // Skip base64 transfer encoding sections
    if line.to_lowercase().contains("content-transfer-encoding: base64") {
        *skip_binary_section = true;
        return false;
    }

    // Skip attachment sections (but we already extracted docs above)
    if line.to_lowercase().contains("content-disposition: attachment") {
        *skip_binary_section = true;
        return false;
    }

    // Skip if we're in a binary section
    if *skip_binary_section {
        debug_stats.binary_lines_skipped += 1;
        // Skip any encoded data lines
        if is_encoded_content_line(line) {
            return false;
        }
    }

    // Enhanced binary content detection - aggressively filter binary content
    if is_binary_attachment_or_content(line) {
        *skip_binary_section = true;
        debug_stats.binary_lines_skipped += 1;
        return false;
    }

    // Skip lines that look like encoded data
    if is_encoded_content_line(line) {
        debug_stats.binary_lines_skipped += 1;
        return false;
    }

    // Keep the line if it's not binary content
    true
}

/// Extract document text from a specific attachment section
fn extract_document_from_attachment_section(email_content: &str, attachment_line: &str) -> Option<String> {
    // Extract filename from Content-Disposition header or nearby lines
    let filename = extract_filename_from_attachment_section(email_content, attachment_line)?;

    // Check if it's a document file we can process
    if !is_document_file(&filename) {
        return None;
    }

    // Find the base64 content for this attachment
    let lines: Vec<&str> = email_content.lines().collect();
    let mut found_attachment = false;
    let mut in_base64_section = false;
    let mut base64_data = String::new();
    let mut lines_processed = 0;

    for line in lines {
        lines_processed += 1;

        // Look for our specific attachment
        if line.contains("filename=") && line.contains(&filename) {
            found_attachment = true;
            continue;
        }

        if found_attachment {
            // Look for base64 encoding marker
            if line.to_lowercase().contains("content-transfer-encoding: base64") {
                in_base64_section = true;
                continue;
            }

            // Collect base64 data
            if in_base64_section {
                // Stop at real MIME boundary (starts with --)
                if line.starts_with("--") {
                    break;
                }

                // Skip header lines that come after the base64 marker
                if line.to_lowercase().contains("content-disposition:") ||
                   line.to_lowercase().contains("content-type:") ||
                   line.to_lowercase().contains("filename=") ||
                   line.trim().is_empty() {
                    continue;
                }

                // Check if this looks like base64 data (long lines of base64 characters)
                if line.len() > 20 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
                    base64_data.push_str(line.trim());
                }
            }
        }
    }

    // Extract text from the collected base64 data
    if !base64_data.is_empty() {
        if let Some(text) = extract_text_from_base64_document(&base64_data, &filename) {
            return Some(format!("--- Extracted from {} ---\n{}\n--- End of {} ---", filename, text, filename));
        }
    }

    None
}

/// Extract text content from DOC/DOCX attachments in email
fn extract_document_text_from_email(email_content: &str) -> Option<String> {
    let mut extracted_texts = Vec::new();
    let mut in_base64_attachment = false;
    let mut current_attachment_data = String::new();
    let mut attachment_filename = None;
    let mut attachment_content_type = None;

    for line in email_content.lines() {
        // Detect attachment headers
        if line.to_lowercase().starts_with("content-disposition:") && line.to_lowercase().contains("attachment") {
            // Extract filename
            if let Some(filename_start) = line.find("filename=") {
                let filename_part = &line[filename_start + 9..];
                let filename = filename_part.trim_matches('"').trim_matches('\'');
                attachment_filename = Some(filename.to_string());
            }
        }

        // Detect content type
        if line.to_lowercase().starts_with("content-type:") {
            attachment_content_type = Some(line.to_string());
        }

        // Detect base64 encoding start
        if line.to_lowercase().contains("content-transfer-encoding: base64") {
            in_base64_attachment = true;
            current_attachment_data.clear();
            continue;
        }

        // Collect base64 data
        if in_base64_attachment {
            // Check if this is the end of the attachment (boundary or empty line)
            if line.starts_with("--") || line.trim().is_empty() {
                // Process the collected attachment
                if let (Some(filename), Some(_content_type)) = (&attachment_filename, &attachment_content_type) {
                    if is_document_file(filename) {
                        if let Some(text) = extract_text_from_base64_document(&current_attachment_data, filename) {
                            extracted_texts.push(format!("\n--- Extracted from {} ---\n{}\n--- End of {} ---\n", filename, text, filename));
                        }
                    }
                }

                // Reset state
                in_base64_attachment = false;
                current_attachment_data.clear();
                attachment_filename = None;
                attachment_content_type = None;
            } else if line.len() > 40 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
                // This looks like base64 data
                current_attachment_data.push_str(line);
            }
        }
    }

    if extracted_texts.is_empty() {
        None
    } else {
        Some(extracted_texts.join("\n"))
    }
}

/// Extract filename from attachment section with robust multiline support
fn extract_filename_from_attachment_section(email_content: &str, attachment_line: &str) -> Option<String> {
    // First try to find filename in the current line
    if let Some(filename) = extract_filename_from_line(attachment_line) {
        return Some(filename);
    }

    // If not found, search nearby lines for filename
    let lines: Vec<&str> = email_content.lines().collect();
    let mut found_attachment_line = false;

    for (i, line) in lines.iter().enumerate() {
        if line.trim() == attachment_line.trim() {
            found_attachment_line = true;

            // Search the next 5 lines for filename
            for j in 1..=5 {
                if i + j < lines.len() {
                    let next_line = lines[i + j];
                    if let Some(filename) = extract_filename_from_line(next_line) {
                        return Some(filename);
                    }

                    // Stop if we hit a boundary or empty line
                    if next_line.starts_with("--") || next_line.trim().is_empty() {
                        break;
                    }
                }
            }
            break;
        }
    }

    None
}

/// Extract filename from a single line with multiple format support
fn extract_filename_from_line(line: &str) -> Option<String> {
    let line_lower = line.to_lowercase();

    // Try different filename patterns
    if let Some(filename_start) = line.find("filename=") {
        let filename_part = &line[filename_start + 9..];
        let filename = filename_part.trim_matches('"').trim_matches('\'').trim();
        if !filename.is_empty() {
            return Some(filename.to_string());
        }
    }

    // Try filename* pattern (RFC 2231)
    if let Some(filename_start) = line_lower.find("filename*=") {
        let filename_part = &line[filename_start + 10..];
        // Handle encoding like: filename*=UTF-8''document.doc
        if let Some(quote_pos) = filename_part.find("''") {
            let filename = &filename_part[quote_pos + 2..];
            let filename = filename.trim_matches('"').trim_matches('\'').trim();
            if !filename.is_empty() {
                return Some(filename.to_string());
            }
        }
    }

    // Try name= pattern
    if let Some(name_start) = line.find("name=") {
        let name_part = &line[name_start + 5..];
        let filename = name_part.trim_matches('"').trim_matches('\'').trim();
        if !filename.is_empty() && (filename.contains(".doc") || filename.contains(".DOC")) {
            return Some(filename.to_string());
        }
    }

    None
}

/// Check if filename indicates a document file we can process
fn is_document_file(filename: &str) -> bool {
    let filename_lower = filename.to_lowercase();
    filename_lower.ends_with(".docx") || filename_lower.ends_with(".doc")
}

/// Extract text from base64-encoded document data
fn extract_text_from_base64_document(base64_data: &str, filename: &str) -> Option<String> {
    use base64::{Engine as _, engine::general_purpose};

    // Decode base64
    let cleaned_base64 = base64_data.replace('\n', "").replace('\r', "").replace(' ', "");

    let binary_data = match general_purpose::STANDARD.decode(&cleaned_base64) {
        Ok(data) => data,
        Err(_) => return None,
    };

    // Extract text based on file type
    if filename.to_lowercase().ends_with(".docx") {
        extract_docx_text(&binary_data)
    } else if filename.to_lowercase().ends_with(".doc") {
        extract_doc_text(&binary_data)
    } else {
        None
    }
}

/// Extract text from DOCX file (ZIP-based format)
fn extract_docx_text(data: &[u8]) -> Option<String> {
    use std::io::Cursor;
    use zip::ZipArchive;

    let cursor = Cursor::new(data);
    let mut archive = ZipArchive::new(cursor).ok()?;

    // Look for document.xml in the DOCX structure
    let mut document_xml = String::new();
    if let Ok(mut file) = archive.by_name("word/document.xml") {
        if file.read_to_string(&mut document_xml).is_ok() {
            return extract_text_from_docx_xml(&document_xml);
        }
    }

    None
}

/// Extract text from DOCX XML content with aggressive cleaning
fn extract_text_from_docx_xml(xml_content: &str) -> Option<String> {
    let mut text_parts = Vec::new();
    let mut current_text = String::new();
    let mut in_text_element = false;
    let mut chars = xml_content.chars().peekable();

    while let Some(ch) = chars.next() {
        if ch == '<' {
            // Start of XML tag - read the entire tag
            let mut tag = String::new();
            tag.push(ch);

            while let Some(tag_ch) = chars.next() {
                tag.push(tag_ch);
                if tag_ch == '>' {
                    break;
                }
            }

            // Check if this is a text element
            if tag.starts_with("<w:t") && !tag.contains("</w:t>") {
                in_text_element = true;
            } else if tag.contains("</w:t>") {
                in_text_element = false;
                // End of text element - save current text
                if !current_text.trim().is_empty() {
                    text_parts.push(current_text.trim().to_string());
                    current_text.clear();
                }
            } else if tag.contains("</w:p>") {
                // End of paragraph - add line break
                if !current_text.trim().is_empty() {
                    text_parts.push(current_text.trim().to_string());
                    current_text.clear();
                }
                text_parts.push("\n".to_string());
            }
        } else if in_text_element && ch.is_ascii_graphic() || ch.is_ascii_whitespace() {
            // Only collect printable characters when inside text elements
            current_text.push(ch);
        }
    }

    // Join all text parts and clean aggressively
    let raw_text = text_parts.join(" ");
    let cleaned_text = super_clean_text(&raw_text);

    if cleaned_text.len() > 10 {
        Some(cleaned_text)
    } else {
        None
    }
}

/// Clean extracted text by removing excessive whitespace and formatting artifacts
fn clean_extracted_text(text: &str) -> String {
    let mut cleaned = String::new();
    let mut prev_was_whitespace = false;

    for line in text.lines() {
        let trimmed = line.trim();

        // Skip empty lines and lines with only formatting characters
        if trimmed.is_empty() ||
           trimmed.len() < 3 ||
           trimmed.chars().all(|c| !c.is_ascii_alphabetic()) {
            continue;
        }

        // Skip lines that look like HTML/XML artifacts
        if trimmed.contains('<') && trimmed.contains('>') {
            continue;
        }

        // Skip lines with excessive special characters (likely formatting)
        let alpha_count = trimmed.chars().filter(|c| c.is_ascii_alphabetic()).count();
        let total_count = trimmed.len();
        if total_count > 0 && (alpha_count as f32 / total_count as f32) < 0.3 {
            continue;
        }

        // Add the cleaned line
        if !prev_was_whitespace || !trimmed.is_empty() {
            cleaned.push_str(trimmed);
            cleaned.push('\n');
            prev_was_whitespace = trimmed.is_empty();
        }
    }

    // Final cleanup: remove excessive newlines
    let mut final_text = String::new();
    let mut newline_count = 0;

    for ch in cleaned.chars() {
        if ch == '\n' {
            newline_count += 1;
            if newline_count <= 2 {  // Allow max 2 consecutive newlines
                final_text.push(ch);
            }
        } else {
            newline_count = 0;
            final_text.push(ch);
        }
    }

    final_text.trim().to_string()
}

/// Extract text from DOC file (binary format) - improved implementation
fn extract_doc_text(data: &[u8]) -> Option<String> {
    // DOC files are complex binary format - this is a simplified approach
    // For production use, consider using a proper DOC parser library

    let content = String::from_utf8_lossy(data);
    let mut extracted_lines = Vec::new();

    // Look for readable text patterns in the binary data
    for line in content.lines() {
        // Filter out lines that are mostly binary/control characters
        let printable_chars: String = line.chars()
            .filter(|c| c.is_ascii_graphic() || c.is_ascii_whitespace())
            .collect();

        // Clean the line further
        let cleaned_line = clean_doc_line(&printable_chars);

        // Only keep lines with substantial readable content
        if cleaned_line.len() > 5 &&
           cleaned_line.chars().filter(|c| c.is_ascii_alphabetic()).count() > 3 {
            extracted_lines.push(cleaned_line);
        }
    }

    // Join lines and apply final cleaning
    let raw_text = extracted_lines.join("\n");
    let cleaned_text = clean_extracted_text(&raw_text);

    if cleaned_text.len() > 20 { // Only return if we got substantial text
        Some(cleaned_text)
    } else {
        None
    }
}

/// Clean individual lines from DOC extraction
fn clean_doc_line(line: &str) -> String {
    let mut cleaned = String::new();
    let mut prev_was_space = false;

    for ch in line.chars() {
        if ch.is_ascii_alphabetic() || ch.is_ascii_digit() ||
           ch == '.' || ch == ',' || ch == ';' || ch == ':' ||
           ch == '!' || ch == '?' || ch == '-' || ch == '\'' || ch == '"' {
            cleaned.push(ch);
            prev_was_space = false;
        } else if ch.is_ascii_whitespace() && !prev_was_space {
            cleaned.push(' ');
            prev_was_space = true;
        }
    }

    cleaned.trim().to_string()
}

/// Extract and convert HTML content from email to clean text
fn extract_html_content_from_email(email_content: &str) -> Option<String> {
    let lines: Vec<&str> = email_content.lines().collect();
    let mut html_content = String::new();
    let mut in_html_section = false;
    let mut collecting_html = false;

    for line in lines {
        // Look for HTML content type
        if line.to_lowercase().contains("content-type: text/html") {
            in_html_section = true;
            continue;
        }

        // Look for base64 or quoted-printable encoding in HTML sections
        if in_html_section {
            if line.to_lowercase().contains("content-transfer-encoding:") {
                let encoding = line.to_lowercase();
                if encoding.contains("base64") {
                    // Handle base64 encoded HTML
                    return extract_base64_html_content(email_content);
                } else if encoding.contains("quoted-printable") {
                    // Handle quoted-printable encoded HTML
                    return extract_quoted_printable_html_content(email_content);
                } else {
                    // Plain HTML content
                    collecting_html = true;
                }
                continue;
            }

            // Start collecting after empty line (end of headers)
            if line.trim().is_empty() && in_html_section {
                collecting_html = true;
                continue;
            }

            // Stop at boundaries
            if line.starts_with("--") && collecting_html {
                break;
            }

            // Collect HTML content
            if collecting_html {
                html_content.push_str(line);
                html_content.push('\n');
            }
        }
    }

    // Convert HTML to text if we collected any
    if !html_content.trim().is_empty() {
        let text = html2text::from_read(html_content.as_bytes(), 80);
        let cleaned_text = clean_html_converted_text(&text);
        if !cleaned_text.trim().is_empty() {
            return Some(cleaned_text);
        }
    }

    None
}

/// Extract base64 encoded HTML content
fn extract_base64_html_content(email_content: &str) -> Option<String> {
    use base64::{Engine as _, engine::general_purpose};

    let lines: Vec<&str> = email_content.lines().collect();
    let mut base64_data = String::new();
    let mut in_html_base64 = false;

    for line in lines {
        if line.to_lowercase().contains("content-type: text/html") {
            in_html_base64 = true;
            continue;
        }

        if in_html_base64 {
            if line.to_lowercase().contains("content-transfer-encoding: base64") {
                continue;
            }

            if line.trim().is_empty() {
                continue;
            }

            if line.starts_with("--") {
                break;
            }

            // Collect base64 data
            if line.len() > 20 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
                base64_data.push_str(line);
            }
        }
    }

    // Decode and convert HTML to text
    if !base64_data.is_empty() {
        if let Ok(html_bytes) = general_purpose::STANDARD.decode(base64_data.replace('\n', "").replace('\r', "")) {
            let html_content = String::from_utf8_lossy(&html_bytes);
            let text = html2text::from_read(html_content.as_bytes(), 80);
            let cleaned_text = clean_html_converted_text(&text);
            if !cleaned_text.trim().is_empty() {
                return Some(cleaned_text);
            }
        }
    }

    None
}

/// Extract quoted-printable encoded HTML content
fn extract_quoted_printable_html_content(email_content: &str) -> Option<String> {
    let lines: Vec<&str> = email_content.lines().collect();
    let mut qp_data = String::new();
    let mut in_html_qp = false;

    for line in lines {
        if line.to_lowercase().contains("content-type: text/html") {
            in_html_qp = true;
            continue;
        }

        if in_html_qp {
            if line.to_lowercase().contains("content-transfer-encoding: quoted-printable") {
                continue;
            }

            if line.trim().is_empty() {
                continue;
            }

            if line.starts_with("--") {
                break;
            }

            // Collect quoted-printable data
            qp_data.push_str(line);
            qp_data.push('\n');
        }
    }

    // Decode quoted-printable and convert HTML to text
    if !qp_data.is_empty() {
        let html_content = decode_quoted_printable(&qp_data);
        let text = html2text::from_read(html_content.as_bytes(), 80);
        let cleaned_text = clean_html_converted_text(&text);
        if !cleaned_text.trim().is_empty() {
            return Some(cleaned_text);
        }
    }

    None
}

/// Simple quoted-printable decoder
fn decode_quoted_printable(input: &str) -> String {
    let mut result = String::new();
    let mut chars = input.chars().peekable();

    while let Some(ch) = chars.next() {
        if ch == '=' {
            if let (Some(hex1), Some(hex2)) = (chars.next(), chars.next()) {
                if let Ok(byte_val) = u8::from_str_radix(&format!("{}{}", hex1, hex2), 16) {
                    result.push(byte_val as char);
                } else {
                    result.push(ch);
                    result.push(hex1);
                    result.push(hex2);
                }
            } else {
                result.push(ch);
            }
        } else {
            result.push(ch);
        }
    }

    result
}

/// Clean text converted from HTML with improved spacing and formatting
fn clean_html_converted_text(text: &str) -> String {
    // First pass: Fix common HTML conversion issues
    let mut fixed_text = text.to_string();

    // Fix charset artifacts
    fixed_text = fixed_text.replace("charset\"us-ascii\"", "");
    fixed_text = fixed_text.replace("charset\"iso-8859-1\"", "");
    fixed_text = fixed_text.replace("charset\"koi8-r\"", "");

    // Fix spacing issues around common patterns
    fixed_text = fixed_text.replace("nbs p;", " ");
    fixed_text = fixed_text.replace("nbsp;", " ");
    fixed_text = fixed_text.replace("&nbsp;", " ");

    // Fix email patterns
    fixed_text = fixed_text.replace("[mailto:", "");
    fixed_text = fixed_text.replace("]", "");

    // Fix missing spaces after periods and commas
    let mut result = String::new();
    let chars: Vec<char> = fixed_text.chars().collect();

    for i in 0..chars.len() {
        let ch = chars[i];
        result.push(ch);

        // Add space after punctuation if missing
        if (ch == '.' || ch == ',' || ch == ':' || ch == ';') &&
           i + 1 < chars.len() &&
           chars[i + 1].is_ascii_alphabetic() {
            result.push(' ');
        }
    }

    // Apply moderate cleaning (less aggressive than super_clean_text)
    moderate_clean_text(&result)
}

/// Super aggressive text cleaning for documents and HTML
fn super_clean_text(text: &str) -> String {
    let mut cleaned_lines = Vec::new();

    for line in text.lines() {
        let trimmed = line.trim();

        // Skip empty lines
        if trimmed.is_empty() {
            continue;
        }

        // Skip lines that are too short to be meaningful
        if trimmed.len() < 5 {
            continue;
        }

        // Skip lines with mostly non-alphabetic characters (formatting, CSS, etc.)
        let alpha_count = trimmed.chars().filter(|c| c.is_ascii_alphabetic()).count();
        let total_count = trimmed.len();
        if alpha_count < 3 || (alpha_count as f32 / total_count as f32) < 0.3 {
            continue;
        }

        // Skip lines that look like CSS, XML, or HTML artifacts
        if trimmed.contains("font-family:") ||
           trimmed.contains("font-size:") ||
           trimmed.contains("margin:") ||
           trimmed.contains("padding:") ||
           trimmed.contains("color:") ||
           trimmed.contains("text-decoration:") ||
           trimmed.contains("mso-") ||
           trimmed.contains("xmlns:") ||
           trimmed.contains("xml:") ||
           trimmed.contains("<!") ||
           trimmed.contains("<?") ||
           trimmed.contains("<w:") ||
           trimmed.contains("</w:") ||
           trimmed.contains("<o:") ||
           trimmed.contains("</o:") ||
           trimmed.contains("class=") ||
           trimmed.contains("style=") ||
           trimmed.contains("div class") ||
           trimmed.contains("span class") ||
           trimmed.starts_with("@") ||
           trimmed.starts_with("#") ||
           trimmed.starts_with(".") ||
           trimmed.starts_with("{") ||
           trimmed.starts_with("}") ||
           trimmed.contains("BEHAVIOR:") ||
           trimmed.contains("FONT-FAMILY:") ||
           trimmed.contains("MARGIN:") ||
           trimmed.contains("TEXT-ALIGN:") {
            continue;
        }

        // Skip lines that are mostly punctuation or special characters
        let punct_count = trimmed.chars().filter(|c| c.is_ascii_punctuation()).count();
        if punct_count > alpha_count {
            continue;
        }

        // Skip lines with excessive uppercase (likely headers/formatting)
        let upper_count = trimmed.chars().filter(|c| c.is_ascii_uppercase()).count();
        if upper_count > 0 && (upper_count as f32 / alpha_count as f32) > 0.8 && trimmed.len() < 50 {
            continue;
        }

        // Clean the line itself
        let cleaned_line = clean_line_content(trimmed);
        if !cleaned_line.is_empty() && cleaned_line.len() > 3 {
            cleaned_lines.push(cleaned_line);
        }
    }

    // Join lines and do final cleanup
    let result = cleaned_lines.join("\n");

    // Remove excessive whitespace
    let mut final_result = String::new();
    let mut prev_was_space = false;

    for ch in result.chars() {
        if ch.is_whitespace() {
            if !prev_was_space {
                final_result.push(' ');
                prev_was_space = true;
            }
        } else {
            final_result.push(ch);
            prev_was_space = false;
        }
    }

    final_result.trim().to_string()
}

/// Clean individual line content
fn clean_line_content(line: &str) -> String {
    let mut result = String::new();

    for ch in line.chars() {
        // Only keep printable ASCII characters and basic punctuation
        if ch.is_ascii_alphabetic() ||
           ch.is_ascii_digit() ||
           " .,!?;:()[]{}\"'-".contains(ch) {
            result.push(ch);
        }
    }

    result.trim().to_string()
}

/// Moderate text cleaning - less aggressive than super_clean_text for HTML content
fn moderate_clean_text(text: &str) -> String {
    let mut cleaned_lines = Vec::new();

    for line in text.lines() {
        let trimmed = line.trim();

        // Skip empty lines
        if trimmed.is_empty() {
            continue;
        }

        // More lenient requirements than super_clean_text
        if trimmed.len() >= 3 {
            let alpha_count = trimmed.chars().filter(|c| c.is_ascii_alphabetic()).count();

            // Only require 20% alphabetic characters (vs 30% in super_clean_text)
            if alpha_count >= 2 && (alpha_count as f32 / trimmed.len() as f32) >= 0.2 {
                // Clean the line but keep more content
                let cleaned_line = moderate_clean_line(trimmed);
                if !cleaned_line.is_empty() {
                    cleaned_lines.push(cleaned_line);
                }
            }
        }
    }

    // Join lines with proper spacing
    let result = cleaned_lines.join(" ");

    // Final whitespace normalization
    let mut final_result = String::new();
    let mut prev_was_space = false;

    for ch in result.chars() {
        if ch.is_whitespace() {
            if !prev_was_space {
                final_result.push(' ');
                prev_was_space = true;
            }
        } else {
            final_result.push(ch);
            prev_was_space = false;
        }
    }

    final_result.trim().to_string()
}

/// Moderate line cleaning - preserves more content than super aggressive cleaning
fn moderate_clean_line(line: &str) -> String {
    let mut result = String::new();

    for ch in line.chars() {
        // Keep more characters than super_clean_text
        if ch.is_ascii_alphabetic() ||
           ch.is_ascii_digit() ||
           " .,!?;:()[]{}\"'-@/\\".contains(ch) {
            result.push(ch);
        }
    }

    result.trim().to_string()
}

/// Detect binary attachments and content types we want to exclude
fn is_binary_attachment_or_content(line: &str) -> bool {
    let line_lower = line.to_lowercase();

    // Binary image formats
    if line_lower.contains(".jpg") || line_lower.contains(".jpeg") ||
       line_lower.contains(".png") || line_lower.contains(".gif") ||
       line_lower.contains(".bmp") || line_lower.contains(".tif") ||
       line_lower.contains(".tiff") || line_lower.contains(".webp") {
        return true;
    }

    // Binary document formats (except DOC/DOCX which we extract)
    if line_lower.contains(".pdf") || line_lower.contains(".xls") ||
       line_lower.contains(".xlsx") || line_lower.contains(".ppt") ||
       line_lower.contains(".pptx") || line_lower.contains(".rtf") {
        return true;
    }

    // Archive and compressed formats
    if line_lower.contains(".zip") || line_lower.contains(".rar") ||
       line_lower.contains(".7z") || line_lower.contains(".tar") ||
       line_lower.contains(".gz") || line_lower.contains(".bz2") {
        return true;
    }

    // Executable and binary formats
    if line_lower.contains(".exe") || line_lower.contains(".dll") ||
       line_lower.contains(".bin") || line_lower.contains(".iso") ||
       line_lower.contains(".dmg") || line_lower.contains(".msi") {
        return true;
    }

    // Media formats
    if line_lower.contains(".mp3") || line_lower.contains(".mp4") ||
       line_lower.contains(".avi") || line_lower.contains(".mov") ||
       line_lower.contains(".wav") || line_lower.contains(".flv") {
        return true;
    }

    // Binary content types
    if line_lower.contains("content-type: image/") ||
       line_lower.contains("content-type: video/") ||
       line_lower.contains("content-type: audio/") ||
       line_lower.contains("content-type: application/pdf") ||
       line_lower.contains("content-type: application/zip") ||
       line_lower.contains("content-type: application/octet-stream") ||
       line_lower.contains("content-type: application/x-") {
        return true;
    }

    false
}

/// Detect encoded content lines (base64, quoted-printable, etc.)
fn is_encoded_content_line(line: &str) -> bool {
    let trimmed = line.trim();

    // Skip very short lines
    if trimmed.len() < 3 {
        return false;
    }

    // Base64 detection - lines with only base64 characters
    if trimmed.len() > 10 &&
       trimmed.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
        return true;
    }

    // Short encoded chunks (like the ones in your example: GOQ, jzl, zmd, etc.)
    if trimmed.len() >= 3 && trimmed.len() <= 10 &&
       trimmed.chars().all(|c| c.is_ascii_alphanumeric()) &&
       trimmed.chars().filter(|c| c.is_ascii_uppercase()).count() > 0 &&
       trimmed.chars().filter(|c| c.is_ascii_lowercase()).count() > 0 {
        // This looks like short base64 chunks
        return true;
    }

    // Hex encoded content
    if trimmed.len() > 20 && trimmed.len() % 2 == 0 &&
       trimmed.chars().all(|c| c.is_ascii_hexdigit()) {
        return true;
    }

    // Lines with mostly non-printable or special characters
    let printable_count = trimmed.chars().filter(|c| c.is_ascii_graphic() && c.is_ascii_alphabetic()).count();
    let total_count = trimmed.len();

    if total_count > 5 && (printable_count as f32 / total_count as f32) < 0.3 {
        return true;
    }

    false
}
