cargo:rerun-if-env-changed=ZSTD_SYS_USE_PKG_CONFIG
OUT_DIR = Some(C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\deps;C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Python313\Scripts\;C:\Python313\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Microsoft VS Code\bin;C:\ProgramData\chocolatey\bin;C:\Program Files\Process Lasso\;C:\Program Files\Git\cmd;C:\Users\<USER>\.cargo\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Roaming\npm;;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
debug.c
entropy_common.c
error_private.c
fse_decompress.c
pool.c
threading.c
zstd_common.c
fse_compress.c
hist.c
huf_compress.c
zstd_compress.c
zstd_compress_literals.c
zstd_compress_sequences.c
zstd_compress_superblock.c
zstd_double_fast.c
zstd_fast.c
zstd_lazy.c
zstd_ldm.c
zstd_opt.c
zstd_preSplit.c
zstdmt_compress.c
huf_decompress.c
zstd_ddict.c
zstd_decompress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_decompress_block.c
cover.c
divsufsort.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
fastcover.c
zdict.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstd_v01.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_v02.c
zstd_v03.c
zstd_v04.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_v05.c
zstd_v06.c
zstd_v07.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=zstd
cargo:rustc-link-search=native=C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out
cargo:root=C:\Users\<USER>\Desktop\thunder\email_assistant\ingestion_service\target\debug\build\zstd-sys-d318ed5690fe96a5\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\zstd-sys-2.0.15+zstd.1.5.7\zstd/lib
