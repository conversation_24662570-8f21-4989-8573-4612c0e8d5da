// Thunderbird Email Processing Utility
// Main processor for converting Thunderbird files to enhanced mbox format with threading

use std::fs;
use std::path::{Path, PathBuf};
use std::io;
use chrono::Utc;

use crate::{parse_mbox, parse_mbox_in_batches, ParsedEmail};
use crate::email_threading::{ThreadedEmail, EmailThreader, DuplicateDetector};
use crate::enhanced_mbox::EnhancedMboxWriter;
use mail_parser::{MessageParser, HeaderValue};

#[cfg(target_os = "windows")]
use std::mem;

/// Memory monitoring utilities for debugging RAM usage
struct MemoryMonitor {
    last_memory_mb: f64,
}

impl MemoryMonitor {
    fn new() -> Self {
        Self {
            last_memory_mb: 0.0,
        }
    }

    #[cfg(target_os = "windows")]
    fn get_memory_usage_mb(&self) -> f64 {
        unsafe {
            // Get current process handle
            let handle = winapi::um::processthreadsapi::GetCurrentProcess();
            let mut pmc: winapi::um::psapi::PROCESS_MEMORY_COUNTERS = mem::zeroed();

            // Get memory info
            if winapi::um::psapi::GetProcessMemoryInfo(
                handle,
                &mut pmc as *mut _,
                mem::size_of::<winapi::um::psapi::PROCESS_MEMORY_COUNTERS>() as u32,
            ) != 0 {
                pmc.WorkingSetSize as f64 / (1024.0 * 1024.0)
            } else {
                0.0
            }
        }
    }

    #[cfg(not(target_os = "windows"))]
    fn get_memory_usage_mb(&self) -> f64 {
        // Fallback for non-Windows systems - try to read from /proc/self/status
        if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<f64>() {
                            return kb / 1024.0; // Convert KB to MB
                        }
                    }
                }
            }
        }
        0.0
    }

    fn log_memory_usage(&mut self, _operation: &str) {
        let current_memory_mb = self.get_memory_usage_mb();
        let delta = current_memory_mb - self.last_memory_mb;

        // Only log warnings for high memory usage
        if current_memory_mb > 2000.0 {
            println!("⚠️  High memory usage: {:.1} MB", current_memory_mb);
        }

        if delta > 1000.0 {
            println!("⚠️  Large memory increase: +{:.1} MB", delta);
        }

        self.last_memory_mb = current_memory_mb;
    }
}

/// Processing mode for memory vs threading optimization
#[derive(Debug, Clone, PartialEq)]
pub enum ProcessingMode {
    /// Single-pass streaming (memory efficient, may miss some cross-file threading)
    SinglePass,
    /// Two-pass streaming (memory efficient + perfect threading)
    TwoPass,
    /// Year-based batch processing (controllable batches + perfect threading)
    YearBased { batch_size_years: u32 },
}

/// Main Thunderbird processor
pub struct ThunderbirdProcessor {
    threader: EmailThreader,
    duplicate_detector: DuplicateDetector,
    processed_count: u32,
    duplicate_count: u32,
    error_count: u32,
    processing_mode: ProcessingMode,
    memory_monitor: MemoryMonitor,
}

impl ThunderbirdProcessor {
    pub fn new() -> Self {
        Self::with_mode(ProcessingMode::SinglePass)
    }

    pub fn with_mode(mode: ProcessingMode) -> Self {
        Self {
            threader: EmailThreader::new(),
            duplicate_detector: DuplicateDetector::new(0.85), // 85% similarity threshold
            processed_count: 0,
            duplicate_count: 0,
            error_count: 0,
            processing_mode: mode,
            memory_monitor: MemoryMonitor::new(),
        }
    }

    /// Process a Thunderbird directory structure using the configured processing mode
    pub fn process_thunderbird_directory(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SinglePass => {
                self.process_single_pass(thunderbird_dir, output_mbox_path)
            }
            ProcessingMode::TwoPass => {
                self.process_two_pass(thunderbird_dir, output_mbox_path)
            }
            ProcessingMode::YearBased { batch_size_years } => {
                self.process_year_based(thunderbird_dir, output_mbox_path, *batch_size_years)
            }
        }
    }

    /// Single-pass streaming processing (original implementation)
    fn process_single_pass(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (single-pass): {}", thunderbird_dir);

        self.memory_monitor.log_memory_usage("Starting single-pass processing");

        let mut writer = EnhancedMboxWriter::new(output_mbox_path)?;
        let mut total_emails_processed = 0u32;

        // Find all mbox files in the directory structure
        let mbox_files = self.find_thunderbird_files(thunderbird_dir)?;
        println!("Found {} Thunderbird files to process", mbox_files.len());

        // Process each file individually to avoid memory accumulation
        for (file_path, folder_type) in mbox_files {
            match self.process_single_file_streaming(&file_path, folder_type, &mut writer) {
                Ok(file_email_count) => {
                    total_emails_processed += file_email_count;
                    if file_email_count > 0 {
                        println!("  Processed {} emails from {}", file_email_count, file_path.display());
                    }
                }
                Err(e) => {
                    eprintln!("Error processing file {}: {}", file_path.display(), e);
                    self.error_count += 1;
                }
            }
        }

        writer.close()?;

        println!("Total emails processed: {}", total_emails_processed);
        println!("Duplicates found: {}", self.duplicate_count);
        println!("Threads created: {}", self.threader.threads.len());
        println!("Cases created: {}", self.threader.cases.len());

        Ok(ProcessingReport {
            total_emails: total_emails_processed + self.duplicate_count,
            processed_emails: self.processed_count,
            duplicate_emails: self.duplicate_count,
            error_count: self.error_count,
            threads_created: self.threader.threads.len() as u32,
            cases_created: self.threader.cases.len() as u32,
            output_file: output_mbox_path.to_string(),
        })
    }

    /// Two-pass processing: first pass for initial processing, second pass for threading refinement
    fn process_two_pass(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (two-pass): {}", thunderbird_dir);

        // First pass: Single-pass processing to create initial enhanced mbox
        let temp_output = format!("{}.temp", output_mbox_path);
        let first_pass_report = self.process_single_pass(thunderbird_dir, &temp_output)?;

        println!("First pass complete. Starting threading refinement pass...");

        // Second pass: Read the enhanced mbox and refine threading
        let refined_report = self.refine_threading(&temp_output, output_mbox_path)?;

        // Clean up temporary file
        if let Err(e) = std::fs::remove_file(&temp_output) {
            eprintln!("Warning: Failed to remove temporary file {}: {}", temp_output, e);
        }

        println!("Two-pass processing complete!");
        println!("Threading refinements made: {}", refined_report.threading_improvements);

        Ok(ProcessingReport {
            total_emails: first_pass_report.total_emails,
            processed_emails: first_pass_report.processed_emails,
            duplicate_emails: first_pass_report.duplicate_emails,
            error_count: first_pass_report.error_count + refined_report.error_count,
            threads_created: refined_report.threads_created,
            cases_created: refined_report.cases_created,
            output_file: output_mbox_path.to_string(),
        })
    }

    /// Year-based batch processing for very large datasets
    fn process_year_based(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
        batch_size_years: u32,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (year-based batches): {}", thunderbird_dir);
        println!("Batch size: {} years", batch_size_years);

        // TODO: Implement year-based batch processing
        // For now, fall back to single-pass
        eprintln!("Year-based processing not yet implemented, falling back to single-pass");
        self.process_single_pass(thunderbird_dir, output_mbox_path)
    }

    /// Refine threading by reading enhanced mbox and identifying missed connections
    fn refine_threading(
        &mut self,
        input_mbox_path: &str,
        output_mbox_path: &str,
    ) -> Result<ThreadingRefinementReport, Box<dyn std::error::Error>> {
        println!("Refining threading from: {}", input_mbox_path);

        // Parse the enhanced mbox file to extract threading metadata
        let parsed_emails = parse_mbox(input_mbox_path)?;
        println!("Read {} emails for threading refinement", parsed_emails.len());

        // Convert to threaded emails and extract existing threading metadata
        let mut threaded_emails = self.convert_to_threaded_emails(parsed_emails)?;

        // ACTUAL THREADING REFINEMENT: Preserve existing threading and find missed connections
        println!("Performing threading refinement (preserving existing threads)...");

        // Track initial state (don't clear - preserve existing good threading)
        let initial_thread_count = self.threader.threads.len();
        let initial_case_count = self.threader.cases.len();

        println!("Initial state: {} threads, {} cases", initial_thread_count, initial_case_count);

        // Apply INCREMENTAL threading refinement - only process emails that might need re-threading
        println!("Looking for missed threading connections across {} emails...", threaded_emails.len());

        // Only re-thread emails that don't have thread assignments or might have missed connections
        let mut emails_to_rethread = Vec::new();
        for email in &threaded_emails {
            if email.thread_id.is_none() || email.case_id.is_none() {
                emails_to_rethread.push(email.id);
            }
        }

        if !emails_to_rethread.is_empty() {
            println!("Re-threading {} emails that need refinement...", emails_to_rethread.len());

            // Only re-process emails that need it
            for email in threaded_emails.iter_mut() {
                if emails_to_rethread.contains(&email.id) {
                    // Re-thread this email
                    if let Ok(thread_id) = self.threader.thread_email(email) {
                        email.processing_notes.push(format!("Refinement: Re-threaded into {}", thread_id));
                    }

                    // Re-catalog this email
                    if let Ok(()) = self.threader.catalog_email(email) {
                        if let Some(case_id) = &email.case_id {
                            email.processing_notes.push(format!("Refinement: Re-cataloged into case {}", case_id));
                        }
                    }
                }
            }
        } else {
            println!("All emails already properly threaded and cataloged - no refinement needed");
        }

        // Calculate improvements
        let final_thread_count = self.threader.threads.len();
        let final_case_count = self.threader.cases.len();
        let threading_improvements = (final_thread_count as i32 - initial_thread_count as i32).abs() as u32;

        println!("Threading refinement results:");
        println!("  Final threads: {}", final_thread_count);
        println!("  Final cases: {}", final_case_count);
        println!("  Threading changes: {}", threading_improvements);

        // Write the refined emails
        let mut writer = EnhancedMboxWriter::new(output_mbox_path)?;
        let mut processed_count = 0u32;

        for email in &threaded_emails {
            if !email.is_duplicate {
                writer.write_email(email)?;
                processed_count += 1;
            }
        }

        writer.close()?;

        Ok(ThreadingRefinementReport {
            total_emails: threaded_emails.len() as u32,
            processed_emails: processed_count,
            threading_improvements,
            error_count: 0,
            threads_created: final_thread_count as u32,
            cases_created: final_case_count as u32,
        })
    }

    /// Find all Thunderbird mbox files in a directory or process a single file
    fn find_thunderbird_files(&self, path: &str) -> Result<Vec<(PathBuf, FolderType)>, io::Error> {
        let mut files = Vec::new();
        let path_obj = Path::new(path);

        if path_obj.is_file() {
            // Handle single file
            if self.is_thunderbird_mbox_file(path_obj) {
                let folder_type = self.determine_folder_type(path_obj);
                files.push((path_obj.to_path_buf(), folder_type));
                println!("Processing single mbox file: {}", path);
            } else {
                println!("File {} is not recognized as a Thunderbird mbox file", path);
            }
        } else if path_obj.is_dir() {
            // Handle directory
            self.scan_directory(path_obj, &mut files)?;
        } else {
            return Err(io::Error::new(io::ErrorKind::NotFound, format!("Path not found: {}", path)));
        }

        Ok(files)
    }

    /// Recursively scan directory for Thunderbird files
    fn scan_directory(&self, dir: &Path, files: &mut Vec<(PathBuf, FolderType)>) -> Result<(), io::Error> {
        if !dir.is_dir() {
            return Ok(());
        }

        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                // Recursively scan subdirectories
                self.scan_directory(&path, files)?;
            } else if self.is_thunderbird_mbox_file(&path) {
                let folder_type = self.determine_folder_type(&path);
                files.push((path, folder_type));
            }
        }
        
        Ok(())
    }

    /// Check if a file is a Thunderbird mbox file
    fn is_thunderbird_mbox_file(&self, path: &Path) -> bool {
        // Use existing detection logic from lib.rs
        if let Some(path_str) = path.to_str() {
            crate::is_thunderbird_mbox(path_str)
        } else {
            false
        }
    }

    /// Determine folder type from path and file content
    fn determine_folder_type(&self, path: &Path) -> FolderType {
        let path_str = path.to_string_lossy().to_lowercase();
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();

        // First try to determine from path/filename
        if file_name == "inbox" || path_str.contains("inbox") {
            return FolderType::Inbox;
        } else if file_name == "sent" || path_str.contains("sent") {
            return FolderType::Sent;
        } else if file_name == "drafts" || path_str.contains("drafts") {
            return FolderType::Drafts;
        } else if file_name == "trash" || path_str.contains("trash") {
            return FolderType::Trash;
        }

        // For cleaned files (.mbox extension), check for X-Folder-Type header
        if path_str.ends_with(".mbox") {
            if let Ok(folder_type) = self.detect_folder_type_from_content(path) {
                return folder_type;
            }
        }

        FolderType::Other(file_name.to_string())
    }

    /// Detect folder type by reading X-Folder-Type header from cleaned mbox files
    fn detect_folder_type_from_content(&self, path: &Path) -> Result<FolderType, Box<dyn std::error::Error>> {
        use std::fs::File;
        use std::io::{BufReader, BufRead};

        let file = File::open(path)?;
        let reader = BufReader::new(file);

        // Read first few lines to find X-Folder-Type header
        for line in reader.lines().take(20) {
            let line = line?;
            if line.to_lowercase().starts_with("x-folder-type:") {
                let folder_type_str = line[14..].trim().to_lowercase();
                return Ok(match folder_type_str.as_str() {
                    "inbox" => FolderType::Inbox,
                    "sent" => FolderType::Sent,
                    "drafts" => FolderType::Drafts,
                    "trash" => FolderType::Trash,
                    "junk" => FolderType::Other("junk".to_string()),
                    _ => FolderType::Other(folder_type_str),
                });
            }

            // Stop reading after first email headers
            if line.trim().is_empty() {
                break;
            }
        }

        Err("No X-Folder-Type header found".into())
    }



    /// Process a single mbox file with streaming output to avoid memory accumulation
    fn process_single_file_streaming(
        &mut self,
        file_path: &Path,
        _folder_type: FolderType,
        writer: &mut EnhancedMboxWriter
    ) -> Result<u32, Box<dyn std::error::Error>> {
        let path_str = file_path.to_str()
            .ok_or("Invalid file path")?;

        // Check file size and warn about very large files
        let file_metadata = std::fs::metadata(file_path)?;
        let file_size_gb = file_metadata.len() as f64 / (1024.0 * 1024.0 * 1024.0);

        if file_size_gb > 2.0 {
            eprintln!("⚠️  WARNING: File is very large ({:.1} GB)", file_size_gb);
            eprintln!("   For files this large, consider:");
            eprintln!("   1. Using year-based processing: thunderbird_processor.exe \"path\" \"output.mbox\" year-based");
            eprintln!("   2. Or splitting the file manually before processing");
            eprintln!("   Attempting to process anyway with reduced batch size...");
        }

        println!("  Processing file in batches to avoid memory issues...");

        self.memory_monitor.log_memory_usage("Before parsing mbox file");

        // For now, fall back to the original approach but with smaller batches
        // TODO: Implement proper streaming with callback that doesn't borrow self
        let parsed_emails = parse_mbox(path_str)?;
        let total_emails = parsed_emails.len();
        println!("  Parsed {} emails from {}", total_emails, file_path.display());

        self.memory_monitor.log_memory_usage(&format!("After parsing {} emails", total_emails));

        // Process emails in smaller batches to reduce memory usage
        // Use very small batches for large files
        let batch_size = if file_size_gb > 2.0 { 25 } else { 100 };
        let mut file_processed_count = 0u32;

        println!("  Using batch size: {} emails per batch", batch_size);

        for (batch_num, batch) in parsed_emails.chunks(batch_size).enumerate() {
            self.memory_monitor.log_memory_usage(&format!("Starting batch {} ({} emails)", batch_num + 1, batch.len()));

            // Convert batch to threaded emails
            let mut threaded_emails = self.convert_to_threaded_emails(batch.to_vec())?;

            self.memory_monitor.log_memory_usage(&format!("After converting batch {} to threaded emails", batch_num + 1));

            // Set folder information for each email in batch
            let folder_path_str = file_path.to_string_lossy();
            for email in &mut threaded_emails {
                email.folder_path = Some(folder_path_str.to_string());
                email.email_type = EmailThreader::detect_email_type(&folder_path_str, &email.from, &email.to);
                email.weight = email.email_type.default_weight();
            }

            // Sort batch by date for better threading
            threaded_emails.sort_by(|a, b| {
                a.sent_date.unwrap_or_else(|| Utc::now())
                    .cmp(&b.sent_date.unwrap_or_else(|| Utc::now()))
            });

            // Apply threading, case cataloging, and duplicate detection to batch
            self.memory_monitor.log_memory_usage(&format!("Before threading batch {}", batch_num + 1));
            self.apply_threading(&mut threaded_emails)?;

            self.memory_monitor.log_memory_usage(&format!("Before case cataloging batch {}", batch_num + 1));
            self.apply_case_cataloging(&mut threaded_emails)?;

            self.memory_monitor.log_memory_usage(&format!("Before duplicate detection batch {}", batch_num + 1));
            self.detect_duplicates(&mut threaded_emails)?;

            // Write batch immediately to file
            self.memory_monitor.log_memory_usage(&format!("Before writing batch {}", batch_num + 1));
            for email in &threaded_emails {
                if !email.is_duplicate {
                    writer.write_email(email)?;
                    self.processed_count += 1;
                    file_processed_count += 1;
                } else {
                    self.duplicate_count += 1;
                }
            }

            // Clear the batch from memory explicitly
            drop(threaded_emails);

            self.memory_monitor.log_memory_usage(&format!("After clearing batch {}", batch_num + 1));

            if (batch_num + 1) % 10 == 0 {
                println!("    Processed {} batches ({} emails)...", batch_num + 1, (batch_num + 1) * batch_size);
            }
        }

        Ok(file_processed_count)
    }

    /// Convert ParsedEmail to ThreadedEmail with enhanced metadata
    fn convert_to_threaded_emails(&self, parsed_emails: Vec<ParsedEmail>) -> Result<Vec<ThreadedEmail>, Box<dyn std::error::Error>> {
        let mut threaded_emails = Vec::new();

        for parsed in parsed_emails {
            // Extract threading headers using mail-parser with error handling
            let (message_id, in_reply_to, references) = if let Some(raw_body) = &parsed.plain_text_body_raw {
                match self.extract_threading_headers(raw_body) {
                    Ok(headers) => headers,
                    Err(e) => {
                        eprintln!("Warning: Failed to extract headers for email {}: {}", parsed.id, e);
                        (None, Vec::new(), Vec::new())
                    }
                }
            } else {
                (None, Vec::new(), Vec::new())
            };

            let normalized_subject = parsed.subject.as_ref()
                .map(|s| EmailThreader::normalize_subject(s));

            // Determine email type from folder path (will be set later) and content
            let email_type = EmailThreader::detect_email_type(
                "", // Will be updated when we process files
                &parsed.from,
                &parsed.to
            );

            let threaded = ThreadedEmail {
                id: parsed.id,
                subject: parsed.subject,
                from: parsed.from,
                to: parsed.to,
                sent_date: parsed.sent_date,
                plain_text_body_raw: parsed.plain_text_body_raw,
                html_body_raw: parsed.html_body_raw,
                cleaned_plain_text_body: parsed.cleaned_plain_text_body,
                message_id,
                in_reply_to,
                references,
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: email_type.clone(),
                weight: email_type.default_weight(),
                folder_path: None, // Will be set based on source file
                content_hash: None,
                normalized_subject,
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: Vec::new(),

                // Initialize case cataloging fields
                case_id: None,
                case_subject: None,
                case_participants: Vec::new(),
            };

            threaded_emails.push(threaded);
        }

        Ok(threaded_emails)
    }

    /// Extract threading headers from email content
    fn extract_threading_headers(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        // Try to parse the email content to extract headers
        // If parsing fails, we'll return empty values rather than failing completely
        let message = match MessageParser::default().parse(content.as_bytes()) {
            Some(msg) => msg,
            None => {
                // If parsing fails, try to extract headers manually from the raw content
                return self.extract_headers_manually(content);
            }
        };

        let message_id = message.message_id().map(|id| id.to_string());

        // Extract In-Reply-To headers
        let mut in_reply_to = Vec::new();
        for header_value in message.header_values("In-Reply-To") {
            if let HeaderValue::Text(text) = header_value {
                in_reply_to.push(text.to_string());
            }
        }

        // Extract References headers
        let mut references = Vec::new();
        for header_value in message.header_values("References") {
            if let HeaderValue::Text(text) = header_value {
                // References can contain multiple message IDs separated by spaces
                for reference in text.split_whitespace() {
                    references.push(reference.to_string());
                }
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Fallback method to extract headers manually when mail-parser fails
    fn extract_headers_manually(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        let mut message_id = None;
        let mut in_reply_to = Vec::new();
        let mut references = Vec::new();

        // Look for headers in the first part of the content
        let lines: Vec<&str> = content.lines().take(100).collect(); // Check first 100 lines

        for line in lines {
            let line = line.trim();

            // Extract Message-ID
            if line.starts_with("Message-ID:") || line.starts_with("Message-Id:") {
                if let Some(id) = line.split(':').nth(1) {
                    message_id = Some(id.trim().to_string());
                }
            }

            // Extract In-Reply-To
            if line.starts_with("In-Reply-To:") {
                if let Some(reply_to) = line.split(':').nth(1) {
                    in_reply_to.push(reply_to.trim().to_string());
                }
            }

            // Extract References
            if line.starts_with("References:") {
                if let Some(refs) = line.split(':').nth(1) {
                    for reference in refs.split_whitespace() {
                        references.push(reference.trim().to_string());
                    }
                }
            }

            // Stop at empty line (end of headers)
            if line.is_empty() {
                break;
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Apply threading to all emails
    fn apply_threading(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying email threading across combined Inbox and Sent emails...");

        for email in emails.iter_mut() {
            match self.threader.thread_email(email) {
                Ok(thread_id) => {
                    email.processing_notes.push(format!("Threaded into: {}", thread_id));
                }
                Err(e) => {
                    email.processing_notes.push(format!("Threading error: {}", e));
                }
            }
        }

        println!("Created {} conversation threads", self.threader.threads.len());
        Ok(())
    }

    /// Apply case cataloging to all emails
    fn apply_case_cataloging(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying case cataloging...");

        for email in emails.iter_mut() {
            match self.threader.catalog_email(email) {
                Ok(()) => {
                    if let Some(case_id) = &email.case_id {
                        email.processing_notes.push(format!("Cataloged into case: {}", case_id));
                    }
                }
                Err(e) => {
                    email.processing_notes.push(format!("Case cataloging error: {}", e));
                }
            }
        }

        println!("Created {} email cases", self.threader.cases.len());
        Ok(())
    }

    /// Detect and mark duplicate emails
    fn detect_duplicates(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Detecting duplicate emails...");
        
        for email in emails.iter_mut() {
            if self.duplicate_detector.check_duplicate(email) {
                email.processing_notes.push("Marked as duplicate".to_string());
            }
        }
        
        Ok(())
    }
}

/// Folder type classification
#[derive(Debug, Clone, PartialEq)]
pub enum FolderType {
    Inbox,
    Sent,
    Drafts,
    Trash,
    Other(String),
}

/// Processing report
#[derive(Debug)]
pub struct ProcessingReport {
    pub total_emails: u32,
    pub processed_emails: u32,
    pub duplicate_emails: u32,
    pub error_count: u32,
    pub threads_created: u32,
    pub cases_created: u32,
    pub output_file: String,
}

impl ProcessingReport {
    pub fn print_summary(&self) {
        println!("=== Processing Summary ===");
        println!("Total emails found: {}", self.total_emails);
        println!("Emails processed: {}", self.processed_emails);
        println!("Duplicates removed: {}", self.duplicate_emails);
        println!("Errors encountered: {}", self.error_count);
        println!("Conversation threads: {}", self.threads_created);
        println!("Email cases: {}", self.cases_created);
        println!("Output file: {}", self.output_file);
        println!("========================");
    }
}

/// Threading refinement report for two-pass processing
#[derive(Debug)]
pub struct ThreadingRefinementReport {
    pub total_emails: u32,
    pub processed_emails: u32,
    pub threading_improvements: u32,
    pub error_count: u32,
    pub threads_created: u32,
    pub cases_created: u32,
}


