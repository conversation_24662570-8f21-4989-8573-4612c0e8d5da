# Combine cleaned email files with threading
# Input: F:\EMAILS\full\* (cleaned mbox files)
# Output: F:\EMAILS\full_combined_threaded.mbox

$inputDir = "F:\EMAILS\full"
$outputFile = "F:\EMAILS\full_combined_threaded.mbox"

Write-Host "=== Combining Cleaned Email Files with Threading ==="
Write-Host "Input directory: $inputDir"
Write-Host "Output file: $outputFile"
Write-Host ""

# Get all cleaned email files (exclude any .mbox files, get the year files)
$emailFiles = Get-ChildItem -Path $inputDir | Where-Object { $_.Name -match '^\d{4}[dI]?d?$' } | Sort-Object Name

Write-Host "Found $($emailFiles.Count) cleaned email files:"
foreach ($file in $emailFiles) {
    $sizeMB = [math]::Round($file.Length / 1MB, 1)
    Write-Host "  - $($file.Name) ($sizeMB MB)"
}
Write-Host ""

# Remove existing output file
if (Test-Path $outputFile) {
    Remove-Item $outputFile
    Write-Host "Removed existing output file"
}

Write-Host "Processing files..."
$totalSize = 0
$fileCount = 0

foreach ($file in $emailFiles) {
    $fileCount++
    $inputPath = $file.FullName
    $sizeMB = [math]::Round($file.Length / 1MB, 1)

    Write-Host "[$fileCount/$($emailFiles.Count)] Processing: $($file.Name) ($sizeMB MB)"

    if ($fileCount -eq 1) {
        # First file - copy entirely to start the combined file
        Write-Host "  Creating combined file..."
        Copy-Item $inputPath $outputFile
    } else {
        # Subsequent files - append content
        Write-Host "  Appending to combined file..."
        $content = Get-Content $inputPath -Raw
        Add-Content -Path $outputFile -Value "`n$content" -NoNewline
    }

    $totalSize += $file.Length
    $currentOutputSize = (Get-Item $outputFile).Length
    $currentOutputMB = [math]::Round($currentOutputSize / 1MB, 1)

    Write-Host "  [OK] Added to combined file (Combined size: $currentOutputMB MB)"
}

Write-Host ""
Write-Host "=== Basic Combination Complete ==="

if (Test-Path $outputFile) {
    $finalSize = (Get-Item $outputFile).Length
    $finalSizeMB = [math]::Round($finalSize / 1MB, 1)
    $finalSizeGB = [math]::Round($finalSize / 1GB, 2)
    
    # Count approximate number of emails
    $emailCount = (Get-Content $outputFile | Select-String "^From ").Count
    
    Write-Host "[OK] Combined file created: $outputFile"
    Write-Host "[OK] Final size: $finalSizeMB MB ($finalSizeGB GB)"
    Write-Host "[OK] Approximate emails: $emailCount"
    Write-Host ""
    
    Write-Host "=== Now Processing with Thunderbird Processor for Threading ==="
    Write-Host "This will add proper threading and case management..."
    
    # Now process the combined file with thunderbird processor for threading
    $threadedOutput = "F:\EMAILS\full_combined_final.mbox"
    
    Write-Host "Running thunderbird processor..."
    $startTime = Get-Date
    
    # Use the thunderbird processor to add threading to the combined file
    & "target\release\thunderbird_processor.exe" $outputFile $threadedOutput
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if (Test-Path $threadedOutput) {
        $threadedSize = (Get-Item $threadedOutput).Length
        $threadedSizeMB = [math]::Round($threadedSize / 1MB, 1)
        $threadedSizeGB = [math]::Round($threadedSize / 1GB, 2)
        
        Write-Host ""
        Write-Host "=== FINAL PROCESSING COMPLETE ==="
        Write-Host "[OK] Final threaded file: $threadedOutput"
        Write-Host "[OK] Final size: $threadedSizeMB MB ($threadedSizeGB GB)"
        Write-Host "[OK] Processing time: $($duration.TotalMinutes.ToString('F1')) minutes"
        Write-Host "[OK] All your inbox/sent emails are now combined with proper threading!"
        Write-Host "[OK] Thread and case UUIDs are consistent across all years"
        Write-Host "[OK] Ready for ingestion into the email assistant system"

        # Clean up intermediate file
        Remove-Item $outputFile
        Write-Host "[OK] Cleaned up intermediate file"
    } else {
        Write-Host "✗ Threading processing failed"
        Write-Host "[OK] Basic combined file available at: $outputFile"
    }
} else {
    Write-Host "✗ Failed to create combined file"
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
