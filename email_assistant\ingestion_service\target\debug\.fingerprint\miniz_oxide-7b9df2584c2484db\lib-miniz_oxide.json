{"rustc": 1842507548689473721, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 14936214404469579842, "deps": [[4018467389006652250, "simd_adler32", false, 16175763472218919374], [7911289239703230891, "adler2", false, 8965665015564896579]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-7b9df2584c2484db\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}