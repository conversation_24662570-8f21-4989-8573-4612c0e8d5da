# Thunderbird Email Processor

A comprehensive utility for processing Thunderbird email files into enhanced mbox format with intelligent conversation threading, duplicate detection, and weighted categorization for AI email assistant systems.

## Features

### Core Functionality
- **Thunderbird File Parsing**: Supports both .mbox and .sbd directory structures
- **Email Threading**: RFC 5322 compliant conversation detection using Message-ID, In-Reply-To, and References headers
- **Subject Line Normalization**: Intelligent handling of Re:, Fwd:, and bracketed prefixes
- **Duplicate Detection**: Content-based deduplication using SHA-256 hashing
- **Email Weighting**: Configurable weighting system (66% for sent emails, 34% for received emails)
- **Enhanced mbox Output**: Custom X-headers for metadata preservation

### Email Threading Algorithm
The processor uses a multi-layered approach for conversation detection:

1. **Header-based Threading**: Primary method using RFC 5322 headers
   - Message-ID for unique identification
   - In-Reply-To for direct reply relationships
   - References for conversation chain tracking

2. **Subject-based Fallback**: When headers are missing or malformed
   - Normalized subject line matching
   - Removes common prefixes (Re:, Fwd:, [EXTERNAL], etc.)
   - Handles whitespace and case variations

3. **Temporal Proximity**: Future enhancement for improved accuracy

### Email Categorization and Weighting

#### Email Types
- **Received** (34% weight): Emails in Inbox and similar folders
- **Sent** (66% weight): Emails in Sent Items and outbound folders  
- **Draft** (10% weight): Draft emails
- **Unknown** (50% weight): Uncategorized emails

#### Weighting Rationale
The 66/34 split for sent/received emails optimizes the AI assistant for draft preparation:
- Sent emails provide better examples of the user's writing style and preferences
- Received emails provide context and reference material
- Higher weight on sent emails improves draft quality and consistency

## Installation and Usage

### Prerequisites
- Rust 1.70 or later
- Cargo package manager

### Building
```bash
cd email_assistant/ingestion_service
cargo build --release --bin thunderbird_processor
```

### Usage
```bash
./target/release/thunderbird_processor <thunderbird_directory> <output_mbox_file>
```

#### Arguments
- `thunderbird_directory`: Path to Thunderbird profile directory containing .sbd folders
- `output_mbox_file`: Path for the enhanced mbox output file

#### Example
```bash
./target/release/thunderbird_processor \
    "/path/to/thunderbird/profile/Mail/Local Folders" \
    "processed_emails.mbox"
```

### Output Format

The processor generates an enhanced mbox file with custom X-headers:

```
From <EMAIL> Mon Jan 15 10:00:00 2024
Message-ID: <<EMAIL>>
Date: Mon, 15 Jan 2024 10:00:00 +0000
From: <EMAIL>
To: <EMAIL>
Subject: Project Update Meeting
X-Thread-ID: 550e8400-e29b-41d4-a716-446655440000
X-Conversation-ID: 550e8400-e29b-41d4-a716-446655440000
X-Thread-Position: 1
X-Normalized-Subject: project update meeting
X-Email-Type: received
X-Email-Weight: 0.34
X-Original-Folder: INBOX
X-Content-Hash: abc123def456...
X-Processed-At: 2024-01-15T10:00:00Z
X-Internal-ID: 123e4567-e89b-12d3-a456-426614174000

Email body content here...
```

## Integration with AI Email Assistant

### Ingestion Service Integration
The processed mbox files can be ingested using the enhanced ingestion functions:

```rust
use ingestion_service::ingest_enhanced_mbox_file_with_embeddings;

let ingested_ids = ingest_enhanced_mbox_file_with_embeddings(
    &qdrant_client,
    "processed_emails.mbox",
    "http://localhost:8001/embed"
).await?;
```

### Qdrant Vector Database
The enhanced metadata is stored in Qdrant with the following payload structure:
- Standard email fields (subject, from, to, body, etc.)
- Threading metadata (thread_id, conversation_id, thread_position)
- Weighting information (email_type, email_weight)
- Processing metadata (content_hash, is_duplicate, processing_notes)

## Configuration and Customization

### Email Type Detection
Modify the `detect_email_type` function in `email_threading.rs` to customize folder-based classification:

```rust
pub fn detect_email_type(folder_path: &str, from: &Option<String>, to: &[String]) -> EmailType {
    // Custom logic here
}
```

### Weighting Adjustment
Update the `default_weight` method in `EmailType` to adjust weighting:

```rust
impl EmailType {
    pub fn default_weight(&self) -> f32 {
        match self {
            EmailType::Received => 0.34,  // Adjust as needed
            EmailType::Sent => 0.66,      // Adjust as needed
            // ...
        }
    }
}
```

### Duplicate Detection Threshold
Modify the fuzzy matching threshold in `ThunderbirdProcessor::new()`:

```rust
duplicate_detector: DuplicateDetector::new(0.85), // 85% similarity threshold
```

## Testing

### Unit Tests
```bash
cargo test --lib
```

### Integration Tests
```bash
cargo test
```

### Test Data Generation
The processor includes comprehensive test data generators for validation:

```rust
use crate::test_data::TestDataGenerator;

let test_emails = TestDataGenerator::create_simple_thread();
let complex_subjects = TestDataGenerator::create_complex_subject_thread();
let duplicates = TestDataGenerator::create_duplicate_emails();
```

## Performance Considerations

### Memory Usage
- Threading algorithms require O(n) memory for message tracking
- Large mailboxes may require chunked processing for memory efficiency

### Processing Speed
- Typical processing rate: 1000-5000 emails per minute
- Performance scales with email content size and threading complexity

### Optimization Tips
1. Process smaller batches for very large mailboxes
2. Use SSD storage for improved I/O performance
3. Increase duplicate detection threshold for faster processing

## Troubleshooting

### Common Issues

#### "Failed to parse email for header extraction"
- Indicates malformed email content
- Check for corrupted mbox files
- Verify Thunderbird export integrity

#### "Threading error: ..."
- Usually caused by circular references in email headers
- Processor continues with subject-based fallback threading

#### High duplicate count
- May indicate multiple exports of the same mailbox
- Review source directory structure
- Consider adjusting duplicate detection threshold

### Debug Mode
Enable detailed logging by setting environment variable:
```bash
RUST_LOG=debug ./target/release/thunderbird_processor ...
```

## Future Enhancements

### Planned Features
- Fuzzy duplicate detection using content similarity
- Machine learning-based conversation detection
- Support for additional email formats (PST, EML)
- Incremental processing for large mailboxes
- Advanced filtering and categorization rules

### Contributing
1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Submit a pull request

## License

This project is part of the AI Email Assistant system and follows the same licensing terms.
