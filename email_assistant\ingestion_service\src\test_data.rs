// Test Data Generation for Email Threading and Duplicate Detection
// Creates comprehensive test datasets for validation

use chrono::{Utc, TimeZone};
use uuid::Uuid;
use crate::email_threading::{ThreadedEmail, EmailType};

/// Generate test email datasets for various threading scenarios
pub struct TestDataGenerator;

impl TestDataGenerator {
    /// Create a simple conversation thread (3 emails)
    pub fn create_simple_thread() -> Vec<ThreadedEmail> {
        let base_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 0, 0).unwrap();
        
        vec![
            // Original email
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Project Update Meeting".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string(), "<EMAIL>".to_string()],
                sent_date: Some(base_time),
                plain_text_body_raw: Some("Hi team,\n\nLet's schedule a project update meeting for next week.\n\nBest regards,\nAlice".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Hi team,\n\nLet's schedule a project update meeting for next week.\n\nBest regards,\nAlice".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec![],
                references: vec![],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("project update meeting".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },
            
            // Reply 1
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Re: Project Update Meeting".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string(), "<EMAIL>".to_string()],
                sent_date: Some(base_time + chrono::Duration::hours(2)),
                plain_text_body_raw: Some("Sounds good! How about Tuesday at 2 PM?\n\nBob".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Sounds good! How about Tuesday at 2 PM?\n\nBob".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec!["<<EMAIL>>".to_string()],
                references: vec!["<<EMAIL>>".to_string()],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("project update meeting".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },

            // Reply 2
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Re: Project Update Meeting".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string(), "<EMAIL>".to_string()],
                sent_date: Some(base_time + chrono::Duration::hours(4)),
                plain_text_body_raw: Some("Tuesday works for me too. I'll send out the agenda.\n\nCharlie".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Tuesday works for me too. I'll send out the agenda.\n\nCharlie".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec!["<<EMAIL>>".to_string()],
                references: vec!["<<EMAIL>>".to_string(), "<<EMAIL>>".to_string()],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("project update meeting".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },
        ]
    }

    /// Create emails with complex subject line variations
    pub fn create_complex_subject_thread() -> Vec<ThreadedEmail> {
        let base_time = Utc.with_ymd_and_hms(2024, 1, 16, 9, 0, 0).unwrap();
        
        vec![
            // Original with bracketed prefix
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("[EXTERNAL] Important Security Update".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time),
                plain_text_body_raw: Some("Please review the attached security update.".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Please review the attached security update.".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec![],
                references: vec![],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("important security update".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },

            // Reply with multiple prefixes
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("RE: [EXTERNAL] Important Security Update".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time + chrono::Duration::hours(1)),
                plain_text_body_raw: Some("Thanks for the update. We'll implement this immediately.".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Thanks for the update. We'll implement this immediately.".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec!["<<EMAIL>>".to_string()],
                references: vec!["<<EMAIL>>".to_string()],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Sent,
                weight: 0.66,
                folder_path: Some("Sent Items".to_string()),
                content_hash: None,
                normalized_subject: Some("important security update".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },
        ]
    }

    /// Create duplicate emails for testing duplicate detection
    pub fn create_duplicate_emails() -> Vec<ThreadedEmail> {
        let base_time = Utc.with_ymd_and_hms(2024, 1, 17, 14, 30, 0).unwrap();
        let base_email = ThreadedEmail {
            id: Uuid::new_v4(),
            subject: Some("Weekly Report".to_string()),
            from: Some("<EMAIL>".to_string()),
            to: vec!["<EMAIL>".to_string()],
            sent_date: Some(base_time),
            plain_text_body_raw: Some("Here's this week's report with key metrics and updates.".to_string()),
            html_body_raw: None,
            cleaned_plain_text_body: Some("Here's this week's report with key metrics and updates.".to_string()),
            message_id: Some("<<EMAIL>>".to_string()),
            in_reply_to: vec![],
            references: vec![],
            thread_id: None,
            conversation_id: None,
            thread_position: None,
            email_type: EmailType::Received,
            weight: 0.34,
            folder_path: Some("INBOX".to_string()),
            content_hash: None,
            normalized_subject: Some("weekly report".to_string()),
            is_duplicate: false,
            duplicate_of: None,
            processing_notes: vec![],
            case_id: None,
            case_subject: None,
            case_participants: vec![],
        };

        // Create exact duplicate
        let mut duplicate = base_email.clone();
        duplicate.id = Uuid::new_v4();
        duplicate.sent_date = Some(base_time + chrono::Duration::minutes(5)); // Slightly different time

        vec![base_email, duplicate]
    }

    /// Create mixed sent/received emails for weight testing
    pub fn create_mixed_folder_emails() -> Vec<ThreadedEmail> {
        let base_time = Utc.with_ymd_and_hms(2024, 1, 18, 11, 0, 0).unwrap();
        
        vec![
            // Received email
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Budget Proposal".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time),
                plain_text_body_raw: Some("Please review the attached budget proposal.".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Please review the attached budget proposal.".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec![],
                references: vec![],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("budget proposal".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },

            // Sent reply
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Re: Budget Proposal".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time + chrono::Duration::hours(2)),
                plain_text_body_raw: Some("Looks good! I approve the budget as proposed.".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Looks good! I approve the budget as proposed.".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec!["<<EMAIL>>".to_string()],
                references: vec!["<<EMAIL>>".to_string()],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Sent,
                weight: 0.66,
                folder_path: Some("Sent Items".to_string()),
                content_hash: None,
                normalized_subject: Some("budget proposal".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },
        ]
    }

    /// Create emails with missing headers for fallback testing
    pub fn create_broken_threading_emails() -> Vec<ThreadedEmail> {
        let base_time = Utc.with_ymd_and_hms(2024, 1, 19, 16, 0, 0).unwrap();
        
        vec![
            // Email with missing Message-ID
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("System Maintenance Notice".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time),
                plain_text_body_raw: Some("Scheduled maintenance this weekend.".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("Scheduled maintenance this weekend.".to_string()),
                message_id: None, // Missing Message-ID
                in_reply_to: vec![],
                references: vec![],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("system maintenance notice".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },

            // Reply with broken references
            ThreadedEmail {
                id: Uuid::new_v4(),
                subject: Some("Re: System Maintenance Notice".to_string()),
                from: Some("<EMAIL>".to_string()),
                to: vec!["<EMAIL>".to_string()],
                sent_date: Some(base_time + chrono::Duration::hours(1)),
                plain_text_body_raw: Some("What time will the maintenance start?".to_string()),
                html_body_raw: None,
                cleaned_plain_text_body: Some("What time will the maintenance start?".to_string()),
                message_id: Some("<<EMAIL>>".to_string()),
                in_reply_to: vec!["<<EMAIL>>".to_string()], // Broken reference
                references: vec!["<<EMAIL>>".to_string()],
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: EmailType::Received,
                weight: 0.34,
                folder_path: Some("INBOX".to_string()),
                content_hash: None,
                normalized_subject: Some("system maintenance notice".to_string()),
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: vec![],
                case_id: None,
                case_subject: None,
                case_participants: vec![],
            },
        ]
    }

    /// Generate all test datasets
    pub fn generate_all_test_data() -> Vec<Vec<ThreadedEmail>> {
        vec![
            Self::create_simple_thread(),
            Self::create_complex_subject_thread(),
            Self::create_duplicate_emails(),
            Self::create_mixed_folder_emails(),
            Self::create_broken_threading_emails(),
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simple_thread_generation() {
        let thread = TestDataGenerator::create_simple_thread();
        assert_eq!(thread.len(), 3);
        
        // Check threading relationships
        assert!(thread[0].in_reply_to.is_empty());
        assert_eq!(thread[1].in_reply_to, vec!["<<EMAIL>>"]);
        assert_eq!(thread[2].in_reply_to, vec!["<<EMAIL>>"]);
        
        // Check subject normalization
        assert_eq!(thread[0].normalized_subject, Some("project update meeting".to_string()));
        assert_eq!(thread[1].normalized_subject, Some("project update meeting".to_string()));
        assert_eq!(thread[2].normalized_subject, Some("project update meeting".to_string()));
    }

    #[test]
    fn test_duplicate_emails_generation() {
        let duplicates = TestDataGenerator::create_duplicate_emails();
        assert_eq!(duplicates.len(), 2);
        
        // Should have same content but different IDs
        assert_ne!(duplicates[0].id, duplicates[1].id);
        assert_eq!(duplicates[0].subject, duplicates[1].subject);
        assert_eq!(duplicates[0].cleaned_plain_text_body, duplicates[1].cleaned_plain_text_body);
    }

    #[test]
    fn test_mixed_folder_weights() {
        let mixed = TestDataGenerator::create_mixed_folder_emails();
        assert_eq!(mixed.len(), 2);
        
        // Check weights
        assert_eq!(mixed[0].weight, 0.34); // Received
        assert_eq!(mixed[1].weight, 0.66); // Sent
        
        // Check email types
        assert_eq!(mixed[0].email_type, EmailType::Received);
        assert_eq!(mixed[1].email_type, EmailType::Sent);
    }

    #[test]
    fn test_complex_subject_normalization() {
        let complex = TestDataGenerator::create_complex_subject_thread();
        assert_eq!(complex.len(), 2);
        
        // Both should normalize to the same subject
        assert_eq!(complex[0].normalized_subject, Some("important security update".to_string()));
        assert_eq!(complex[1].normalized_subject, Some("important security update".to_string()));
    }

    #[test]
    fn test_broken_threading_emails() {
        let broken = TestDataGenerator::create_broken_threading_emails();
        assert_eq!(broken.len(), 2);
        
        // First email has no Message-ID
        assert!(broken[0].message_id.is_none());
        
        // Second email has broken references
        assert_eq!(broken[1].in_reply_to, vec!["<<EMAIL>>"]);
    }
}
