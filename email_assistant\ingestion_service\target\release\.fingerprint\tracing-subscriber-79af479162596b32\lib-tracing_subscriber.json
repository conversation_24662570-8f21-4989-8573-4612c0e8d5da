{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 7872416515397446104, "deps": [[1017461770342116999, "sharded_slab", false, 1830412912854118330], [1359731229228270592, "thread_local", false, 1082142421939301230], [3424551429995674438, "tracing_core", false, 2838600795696031629], [3666196340704888985, "smallvec", false, 7446956901728546240], [8614575489689151157, "nu_ansi_term", false, 6575782117303559391], [10806489435541507125, "tracing_log", false, 2591608153572000946]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-79af479162596b32\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}