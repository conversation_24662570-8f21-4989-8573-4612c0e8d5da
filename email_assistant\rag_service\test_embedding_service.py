#!/usr/bin/env python3
"""
Test script for the embedding service
Tests the /embed and /embed_batch endpoints
"""

import requests
import json
import time
import sys

# Configuration
BASE_URL = "http://localhost:8003"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False

def test_single_embedding():
    """Test single text embedding"""
    print("\nTesting single embedding...")
    
    test_text = "Hello, this is a test email about project updates."
    
    payload = {
        "text": test_text,
        "normalize": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/embed", json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Single embedding successful")
            print(f"  Model: {data['model_name']}")
            print(f"  Dimension: {data['dimension']}")
            print(f"  Embedding preview: {data['embedding'][:5]}...")
            return True
        else:
            print(f"✗ Single embedding failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
    except Exception as e:
        print(f"✗ Single embedding error: {e}")
        return False

def test_batch_embedding():
    """Test batch text embedding"""
    print("\nTesting batch embedding...")
    
    test_texts = [
        "Hello, this is the first test email.",
        "This is the second email about meeting schedules.",
        "The third email discusses project deliverables."
    ]
    
    payload = {
        "texts": test_texts,
        "normalize": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/embed_batch", json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Batch embedding successful")
            print(f"  Model: {data['model_name']}")
            print(f"  Dimension: {data['dimension']}")
            print(f"  Count: {data['count']}")
            print(f"  First embedding preview: {data['embeddings'][0][:5]}...")
            return True
        else:
            print(f"✗ Batch embedding failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
    except Exception as e:
        print(f"✗ Batch embedding error: {e}")
        return False

def test_similarity():
    """Test embedding similarity calculation"""
    print("\nTesting embedding similarity...")
    
    # Get embeddings for similar texts
    text1 = "Meeting scheduled for tomorrow at 2 PM"
    text2 = "Tomorrow's meeting is at 2:00 PM"
    text3 = "The weather is nice today"
    
    embeddings = []
    for text in [text1, text2, text3]:
        payload = {"text": text, "normalize": True}
        response = requests.post(f"{BASE_URL}/embed", json=payload)
        if response.status_code == 200:
            embeddings.append(response.json()["embedding"])
        else:
            print(f"✗ Failed to get embedding for: {text}")
            return False
    
    # Calculate cosine similarity
    import numpy as np
    
    def cosine_similarity(a, b):
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    sim_1_2 = cosine_similarity(embeddings[0], embeddings[1])
    sim_1_3 = cosine_similarity(embeddings[0], embeddings[2])
    
    print(f"✓ Similarity test completed")
    print(f"  Similarity between similar texts (1-2): {sim_1_2:.4f}")
    print(f"  Similarity between different texts (1-3): {sim_1_3:.4f}")
    
    if sim_1_2 > sim_1_3:
        print("✓ Similarity test passed: Similar texts have higher similarity")
        return True
    else:
        print("✗ Similarity test failed: Similar texts should have higher similarity")
        return False

def wait_for_service(max_wait=30):
    """Wait for the service to be ready"""
    print(f"Waiting for service to be ready (max {max_wait}s)...")
    
    for i in range(max_wait):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                data = response.json()
                if data.get("model_loaded", False):
                    print("✓ Service is ready!")
                    return True
                else:
                    print(f"  Model not loaded yet... ({i+1}s)")
            else:
                print(f"  Service not ready... ({i+1}s)")
        except:
            print(f"  Waiting for service... ({i+1}s)")
        
        time.sleep(1)
    
    print("✗ Service did not become ready in time")
    return False

def main():
    """Run all tests"""
    print("=== Embedding Service Test Suite ===")
    
    # Wait for service to be ready
    if not wait_for_service():
        sys.exit(1)
    
    # Run tests
    tests = [
        test_health_check,
        test_single_embedding,
        test_batch_embedding,
        test_similarity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed!")
        sys.exit(0)
    else:
        print("✗ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
