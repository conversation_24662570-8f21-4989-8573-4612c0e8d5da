# AI-Assisted Email Response System - Local Development Guide

## Overview
This guide outlines the local development setup for the AI-Assisted Email Response System, designed to run entirely on the developer's local machine without Docker containerization.

## Architecture Overview

### Local Services Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Desktop App   │    │  RAG Service    │    │ Embedding Svc   │
│    (Tauri)      │◄──►│   (Python)      │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Ingestion Svc   │◄──►│   PostgreSQL    │    │   Ollama LLM    │
│    (Rust)       │    │  + pgvector     │    │   (Local)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Communication Protocol
- **HTTP APIs**: All inter-service communication via REST APIs
- **Local Ports**: Each service runs on dedicated local ports
- **Configuration**: Environment files for service discovery

## Prerequisites

### Required Software
1. **PostgreSQL 16** - Local database installation
2. **Rust** - Latest stable toolchain (1.70+)
3. **Python 3.9+** - With pip and virtual environment support
4. **Node.js 18+** - For Tauri desktop application
5. **Ollama** - For local LLM hosting

### System Requirements
- **RAM**: Minimum 16GB (recommended 32GB for LLM)
- **Storage**: 50GB free space for models and data
- **OS**: Windows 10+, macOS 12+, or Linux (Ubuntu 20.04+)

## Project Structure
```
email_assistant/
├── ingestion_service/          # Rust email parsing service
├── rag_service/               # Python RAG and LLM service
├── desktop_app/               # Tauri desktop application
├── scripts/                   # Setup and management scripts
│   ├── setup_postgres.sh     # PostgreSQL setup (Linux/macOS)
│   ├── setup_postgres.ps1    # PostgreSQL setup (Windows)
│   ├── start_services.sh     # Service startup script
│   └── stop_services.sh      # Service shutdown script
├── config/                    # Configuration files
│   ├── database.env          # Database configuration
│   ├── services.env          # Service endpoints
│   └── logging.conf          # Logging configuration
├── logs/                      # Application logs
├── data/                      # Local data storage
├── spec.md                    # Project specification
├── todo.md                    # Development blueprint
├── strict_guidelines.md       # Development guidelines
└── tasklist.md               # Task management
```

## Service Configuration

### Port Allocation
- **PostgreSQL**: 5432 (default)
- **Embedding Service**: 8001
- **Ingestion Service**: 8002
- **RAG Service**: 8003
- **Desktop App**: 8004 (internal Tauri)
- **Ollama**: 11434 (default)

### Environment Configuration
Each service uses environment files for configuration:
- Database connection strings
- Service endpoint URLs
- Model paths and configurations
- Logging levels and paths

## Development Workflow

### Initial Setup
1. Run PostgreSQL setup script
2. Install Python dependencies in virtual environment
3. Install Rust dependencies
4. Initialize Tauri project
5. Download and configure local LLM models

### Daily Development
1. Start PostgreSQL service
2. Activate Python virtual environment
3. Start embedding service
4. Start ingestion service (Rust)
5. Start RAG service (Python)
6. Launch desktop application (Tauri)

### Service Management
- **Startup**: Use orchestration scripts for proper service order
- **Monitoring**: Check service health via HTTP endpoints
- **Logging**: Centralized logging to local files
- **Shutdown**: Graceful shutdown with cleanup

## Security Considerations

### Local Security
- Database access restricted to local connections
- API endpoints bound to localhost only
- No external network exposure by default
- Secure file permissions for configuration files

### Data Privacy
- All data processing happens locally
- No cloud service dependencies for core functionality
- Optional cloud LLM integration with pseudonymisation
- Local storage encryption recommended

## Performance Optimization

### Resource Management
- Memory allocation tuning for local LLM
- Database connection pooling
- Efficient inter-service communication
- Background processing for large email imports

### Monitoring
- Service health checks
- Performance metrics collection
- Resource usage monitoring
- Error rate tracking

## Troubleshooting

### Common Issues
1. **Port Conflicts**: Check for conflicting services
2. **Database Connection**: Verify PostgreSQL service status
3. **Python Dependencies**: Ensure virtual environment activation
4. **Rust Compilation**: Check toolchain version compatibility
5. **Model Loading**: Verify model file paths and permissions

### Debug Mode
- Enable verbose logging for all services
- Use development configuration files
- Monitor inter-service communication
- Check resource utilization

## Installation Package Creation

### Distribution Strategy
The final phase includes creating comprehensive installation packages for deployment to other computers:

#### Cross-Platform Installers
- **Windows**: .msi installer with automated dependency management
- **macOS**: .pkg installer with system integration
- **Linux**: .deb/.rpm packages for major distributions

#### Automated Setup Features
- **Dependency Installation**: PostgreSQL, Rust, Python, Node.js, Ollama
- **Database Configuration**: Automated schema setup and user creation
- **Service Registration**: System service registration for background processes
- **Configuration Migration**: Export/import settings between installations

#### User Documentation
- **Installation Guide**: Step-by-step installation instructions
- **User Manual**: Complete application usage documentation
- **Troubleshooting Guide**: Common issues and solutions
- **Administrator Guide**: System configuration and maintenance

### Deployment Workflow
1. **Package Creation**: Build platform-specific installers
2. **Dependency Bundling**: Include or automate dependency installation
3. **Configuration Setup**: Automated system configuration
4. **Health Verification**: Post-installation system checks
5. **User Onboarding**: Guided first-run experience

## Next Steps
1. Complete Phase 1 setup tasks
2. Implement core email parsing functionality
3. Set up local database schema
4. Develop embedding generation pipeline
5. Create desktop application interface
6. Build comprehensive installation packages
7. Create user documentation and guides

This local development approach ensures complete control over the development environment while maintaining the flexibility to deploy to various production environments through automated installation packages.
