# Python Virtual Environment Setup Script
# AI-Assisted Email Response System

Write-Host "Setting up Python virtual environment..." -ForegroundColor Green

$projectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$ragServicePath = Join-Path $projectRoot "rag_service"

# Create virtual environment
Write-Host "Creating virtual environment..." -ForegroundColor Yellow
python -m venv "$projectRoot\venv"

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& "$projectRoot\venv\Scripts\Activate.ps1"

# Upgrade pip
Write-Host "Upgrading pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# Install requirements
if (Test-Path "$ragServicePath\requirements.txt") {
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    pip install -r "$ragServicePath\requirements.txt"
} else {
    Write-Host "requirements.txt not found, installing basic dependencies..." -ForegroundColor Yellow
    pip install fastapi uvicorn sentence-transformers langchain python-dotenv requests psycopg2-binary sqlalchemy pytest pytest-asyncio
}

Write-Host "Python environment setup complete!" -ForegroundColor Green
Write-Host "To activate the environment, run: $projectRoot\venv\Scripts\Activate.ps1" -ForegroundColor Yellow
