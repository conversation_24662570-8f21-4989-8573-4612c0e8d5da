# AI-Assisted Email Response System - Performance Benchmarking
# Phase 8: Local Deployment and Testing
#
# This script provides comprehensive performance benchmarking including:
# - Email ingestion performance (Rust service)
# - RAG pipeline performance (Python service)
# - End-to-end draft generation benchmarks
# - Memory and CPU usage monitoring
# - Baseline performance establishment

param(
    [switch]$Ingestion,
    [switch]$RAG,
    [switch]$EndToEnd,
    [switch]$All,
    [int]$Iterations = 10,
    [int]$Concurrency = 1,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ProjectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$script:BenchmarkResultsDir = Join-Path $ProjectRoot "benchmark_results"
$script:LogFile = Join-Path $BenchmarkResultsDir "benchmark_execution.log"

# Service endpoints
$script:Services = @{
    "ingestion" = "http://localhost:8080"
    "rag" = "http://localhost:8003"
}

function Write-BenchmarkLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Ensure benchmark results directory exists
    if (-not (Test-Path $script:BenchmarkResultsDir)) {
        New-Item -ItemType Directory -Path $script:BenchmarkResultsDir -Force | Out-Null
    }
    
    # Write to log file
    Add-Content -Path $script:LogFile -Value $logEntry
    
    # Write to console with colors
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        default { Write-Host $logEntry }
    }
}

function Test-ServiceAvailability {
    param([string]$ServiceName, [string]$Url)
    
    try {
        $response = Invoke-WebRequest -Uri "$Url/health" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-BenchmarkLog "$ServiceName service is available" "SUCCESS"
            return $true
        }
    } catch {
        Write-BenchmarkLog "$ServiceName service is not available at $Url" "ERROR"
        return $false
    }
    
    return $false
}

function Get-SystemMetrics {
    """Get current system performance metrics"""
    
    $cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $memoryUsage = [math]::Round(($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize * 100, 2)
    
    return @{
        "cpu_usage_percent" = $cpu.Average
        "memory_usage_percent" = $memoryUsage
        "available_memory_mb" = [math]::Round($memory.FreePhysicalMemory / 1024, 2)
        "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
}

function Measure-IngestionPerformance {
    param([int]$Iterations, [int]$Concurrency)
    
    Write-BenchmarkLog "Starting ingestion service performance benchmark..."
    Write-BenchmarkLog "Iterations: $Iterations, Concurrency: $Concurrency"
    
    if (-not (Test-ServiceAvailability -ServiceName "Ingestion" -Url $script:Services.ingestion)) {
        return $null
    }
    
    # Create test email data
    $testEmails = @()
    for ($i = 1; $i -le $Iterations; $i++) {
        $testEmails += @{
            "subject" = "Performance Test Email $i"
            "sender" = "test$<EMAIL>"
            "content" = "This is a performance test email with content that simulates a real email. " * 10
            "timestamp" = (Get-Date).AddDays(-$i).ToString("yyyy-MM-dd HH:mm:ss")
        }
    }
    
    $results = @()
    $startTime = Get-Date
    $systemMetricsBefore = Get-SystemMetrics
    
    Write-BenchmarkLog "Running $Iterations ingestion operations..."
    
    for ($i = 0; $i -lt $Iterations; $i++) {
        $email = $testEmails[$i]
        $operationStart = Get-Date
        
        try {
            $response = Invoke-RestMethod -Uri "$($script:Services.ingestion)/ingest" -Method POST -Body ($email | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
            $operationEnd = Get-Date
            $duration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i + 1
                "duration_ms" = $duration
                "success" = $true
                "response_size" = ($response | ConvertTo-Json).Length
            }
            
            if ($script:Verbose) {
                Write-BenchmarkLog "Iteration $($i + 1): ${duration}ms"
            }
            
        } catch {
            $operationEnd = Get-Date
            $duration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i + 1
                "duration_ms" = $duration
                "success" = $false
                "error" = $_.Exception.Message
            }
            
            Write-BenchmarkLog "Iteration $($i + 1) failed: $($_.Exception.Message)" "ERROR"
        }
    }
    
    $endTime = Get-Date
    $systemMetricsAfter = Get-SystemMetrics
    $totalDuration = ($endTime - $startTime).TotalMilliseconds
    
    # Calculate statistics
    $successfulResults = $results | Where-Object { $_.success -eq $true }
    $successCount = $successfulResults.Count
    $successRate = [math]::Round(($successCount / $Iterations) * 100, 2)
    
    if ($successfulResults.Count -gt 0) {
        $durations = $successfulResults | ForEach-Object { $_.duration_ms }
        $avgDuration = [math]::Round(($durations | Measure-Object -Average).Average, 2)
        $minDuration = ($durations | Measure-Object -Minimum).Minimum
        $maxDuration = ($durations | Measure-Object -Maximum).Maximum
        $throughput = [math]::Round($successCount / ($totalDuration / 1000), 2)
    } else {
        $avgDuration = 0
        $minDuration = 0
        $maxDuration = 0
        $throughput = 0
    }
    
    $benchmark = @{
        "service" = "ingestion"
        "test_type" = "email_ingestion"
        "iterations" = $Iterations
        "concurrency" = $Concurrency
        "total_duration_ms" = $totalDuration
        "success_count" = $successCount
        "success_rate_percent" = $successRate
        "avg_duration_ms" = $avgDuration
        "min_duration_ms" = $minDuration
        "max_duration_ms" = $maxDuration
        "throughput_ops_per_sec" = $throughput
        "system_metrics_before" = $systemMetricsBefore
        "system_metrics_after" = $systemMetricsAfter
        "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "detailed_results" = $results
    }
    
    Write-BenchmarkLog "Ingestion benchmark completed:" "SUCCESS"
    Write-BenchmarkLog "  Success Rate: $successRate% ($successCount/$Iterations)"
    Write-BenchmarkLog "  Average Duration: ${avgDuration}ms"
    Write-BenchmarkLog "  Throughput: $throughput ops/sec"
    
    return $benchmark
}

function Measure-RAGPerformance {
    param([int]$Iterations, [int]$Concurrency)
    
    Write-BenchmarkLog "Starting RAG service performance benchmark..."
    Write-BenchmarkLog "Iterations: $Iterations, Concurrency: $Concurrency"
    
    if (-not (Test-ServiceAvailability -ServiceName "RAG" -Url $script:Services.rag)) {
        return $null
    }
    
    # Create test draft requests
    $testRequests = @()
    for ($i = 1; $i -le $Iterations; $i++) {
        $testRequests += @{
            "subject" = "Performance Test Legal Inquiry $i"
            "sender" = "client$<EMAIL>"
            "content" = "I need legal advice regarding contract terms and conditions. This is a performance test request with realistic content length. " * 5
        }
    }
    
    $results = @()
    $startTime = Get-Date
    $systemMetricsBefore = Get-SystemMetrics
    
    Write-BenchmarkLog "Running $Iterations draft generation operations..."
    
    for ($i = 0; $i -lt $Iterations; $i++) {
        $request = $testRequests[$i]
        $operationStart = Get-Date
        
        try {
            $response = Invoke-RestMethod -Uri "$($script:Services.rag)/generate_draft" -Method POST -Body ($request | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 60
            $operationEnd = Get-Date
            $duration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i + 1
                "duration_ms" = $duration
                "success" = $true
                "draft_length" = $response.draft.Length
                "context_emails_count" = $response.context_emails_count
                "processing_time_ms" = $response.metadata.processing_time_ms
            }
            
            if ($script:Verbose) {
                Write-BenchmarkLog "Iteration $($i + 1): ${duration}ms (draft: $($response.draft.Length) chars)"
            }
            
        } catch {
            $operationEnd = Get-Date
            $duration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i + 1
                "duration_ms" = $duration
                "success" = $false
                "error" = $_.Exception.Message
            }
            
            Write-BenchmarkLog "Iteration $($i + 1) failed: $($_.Exception.Message)" "ERROR"
        }
    }
    
    $endTime = Get-Date
    $systemMetricsAfter = Get-SystemMetrics
    $totalDuration = ($endTime - $startTime).TotalMilliseconds
    
    # Calculate statistics
    $successfulResults = $results | Where-Object { $_.success -eq $true }
    $successCount = $successfulResults.Count
    $successRate = [math]::Round(($successCount / $Iterations) * 100, 2)
    
    if ($successfulResults.Count -gt 0) {
        $durations = $successfulResults | ForEach-Object { $_.duration_ms }
        $avgDuration = [math]::Round(($durations | Measure-Object -Average).Average, 2)
        $minDuration = ($durations | Measure-Object -Minimum).Minimum
        $maxDuration = ($durations | Measure-Object -Maximum).Maximum
        $throughput = [math]::Round($successCount / ($totalDuration / 1000), 2)
        
        $avgDraftLength = [math]::Round(($successfulResults | ForEach-Object { $_.draft_length } | Measure-Object -Average).Average, 0)
    } else {
        $avgDuration = 0
        $minDuration = 0
        $maxDuration = 0
        $throughput = 0
        $avgDraftLength = 0
    }
    
    $benchmark = @{
        "service" = "rag"
        "test_type" = "draft_generation"
        "iterations" = $Iterations
        "concurrency" = $Concurrency
        "total_duration_ms" = $totalDuration
        "success_count" = $successCount
        "success_rate_percent" = $successRate
        "avg_duration_ms" = $avgDuration
        "min_duration_ms" = $minDuration
        "max_duration_ms" = $maxDuration
        "throughput_ops_per_sec" = $throughput
        "avg_draft_length" = $avgDraftLength
        "system_metrics_before" = $systemMetricsBefore
        "system_metrics_after" = $systemMetricsAfter
        "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "detailed_results" = $results
    }
    
    Write-BenchmarkLog "RAG benchmark completed:" "SUCCESS"
    Write-BenchmarkLog "  Success Rate: $successRate% ($successCount/$Iterations)"
    Write-BenchmarkLog "  Average Duration: ${avgDuration}ms"
    Write-BenchmarkLog "  Throughput: $throughput ops/sec"
    Write-BenchmarkLog "  Average Draft Length: $avgDraftLength characters"
    
    return $benchmark
}

function Measure-EndToEndPerformance {
    param([int]$Iterations)
    
    Write-BenchmarkLog "Starting end-to-end performance benchmark..."
    
    # Check both services are available
    if (-not (Test-ServiceAvailability -ServiceName "Ingestion" -Url $script:Services.ingestion)) {
        Write-BenchmarkLog "Ingestion service required for end-to-end test" "ERROR"
        return $null
    }
    
    if (-not (Test-ServiceAvailability -ServiceName "RAG" -Url $script:Services.rag)) {
        Write-BenchmarkLog "RAG service required for end-to-end test" "ERROR"
        return $null
    }
    
    $results = @()
    $startTime = Get-Date
    $systemMetricsBefore = Get-SystemMetrics
    
    Write-BenchmarkLog "Running $Iterations end-to-end operations..."
    
    for ($i = 1; $i -le $Iterations; $i++) {
        $operationStart = Get-Date
        
        try {
            # Step 1: Ingest an email
            $ingestRequest = @{
                "subject" = "End-to-End Test Email $i"
                "sender" = "e2e-test$<EMAIL>"
                "content" = "This is an end-to-end test email for performance benchmarking. It contains realistic content that would be processed by the system. " * 3
                "timestamp" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            }
            
            $ingestStart = Get-Date
            $ingestResponse = Invoke-RestMethod -Uri "$($script:Services.ingestion)/ingest" -Method POST -Body ($ingestRequest | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
            $ingestEnd = Get-Date
            $ingestDuration = ($ingestEnd - $ingestStart).TotalMilliseconds
            
            # Step 2: Generate a draft response
            $draftRequest = @{
                "subject" = "Re: End-to-End Test Email $i"
                "sender" = "response-test$<EMAIL>"
                "content" = "I need to respond to the end-to-end test email. Please generate an appropriate legal response."
            }
            
            $draftStart = Get-Date
            $draftResponse = Invoke-RestMethod -Uri "$($script:Services.rag)/generate_draft" -Method POST -Body ($draftRequest | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 60
            $draftEnd = Get-Date
            $draftDuration = ($draftEnd - $draftStart).TotalMilliseconds
            
            $operationEnd = Get-Date
            $totalDuration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i
                "total_duration_ms" = $totalDuration
                "ingest_duration_ms" = $ingestDuration
                "draft_duration_ms" = $draftDuration
                "success" = $true
                "draft_length" = $draftResponse.draft.Length
                "context_emails_count" = $draftResponse.context_emails_count
            }
            
            if ($script:Verbose) {
                Write-BenchmarkLog "E2E Iteration ${i}: ${totalDuration}ms (ingest: ${ingestDuration}ms, draft: ${draftDuration}ms)"
            }
            
        } catch {
            $operationEnd = Get-Date
            $totalDuration = ($operationEnd - $operationStart).TotalMilliseconds
            
            $results += @{
                "iteration" = $i
                "total_duration_ms" = $totalDuration
                "success" = $false
                "error" = $_.Exception.Message
            }
            
            Write-BenchmarkLog "E2E Iteration $i failed: $($_.Exception.Message)" "ERROR"
        }
    }
    
    $endTime = Get-Date
    $systemMetricsAfter = Get-SystemMetrics
    $totalTestDuration = ($endTime - $startTime).TotalMilliseconds
    
    # Calculate statistics
    $successfulResults = $results | Where-Object { $_.success -eq $true }
    $successCount = $successfulResults.Count
    $successRate = [math]::Round(($successCount / $Iterations) * 100, 2)
    
    if ($successfulResults.Count -gt 0) {
        $totalDurations = $successfulResults | ForEach-Object { $_.total_duration_ms }
        $avgTotalDuration = [math]::Round(($totalDurations | Measure-Object -Average).Average, 2)
        $minTotalDuration = ($totalDurations | Measure-Object -Minimum).Minimum
        $maxTotalDuration = ($totalDurations | Measure-Object -Maximum).Maximum
        
        $ingestDurations = $successfulResults | ForEach-Object { $_.ingest_duration_ms }
        $avgIngestDuration = [math]::Round(($ingestDurations | Measure-Object -Average).Average, 2)
        
        $draftDurations = $successfulResults | ForEach-Object { $_.draft_duration_ms }
        $avgDraftDuration = [math]::Round(($draftDurations | Measure-Object -Average).Average, 2)
        
        $throughput = [math]::Round($successCount / ($totalTestDuration / 1000), 2)
    } else {
        $avgTotalDuration = 0
        $minTotalDuration = 0
        $maxTotalDuration = 0
        $avgIngestDuration = 0
        $avgDraftDuration = 0
        $throughput = 0
    }
    
    $benchmark = @{
        "service" = "end_to_end"
        "test_type" = "full_workflow"
        "iterations" = $Iterations
        "total_test_duration_ms" = $totalTestDuration
        "success_count" = $successCount
        "success_rate_percent" = $successRate
        "avg_total_duration_ms" = $avgTotalDuration
        "min_total_duration_ms" = $minTotalDuration
        "max_total_duration_ms" = $maxTotalDuration
        "avg_ingest_duration_ms" = $avgIngestDuration
        "avg_draft_duration_ms" = $avgDraftDuration
        "throughput_ops_per_sec" = $throughput
        "system_metrics_before" = $systemMetricsBefore
        "system_metrics_after" = $systemMetricsAfter
        "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "detailed_results" = $results
    }
    
    Write-BenchmarkLog "End-to-end benchmark completed:" "SUCCESS"
    Write-BenchmarkLog "  Success Rate: $successRate% ($successCount/$Iterations)"
    Write-BenchmarkLog "  Average Total Duration: ${avgTotalDuration}ms"
    Write-BenchmarkLog "  Average Ingest Duration: ${avgIngestDuration}ms"
    Write-BenchmarkLog "  Average Draft Duration: ${avgDraftDuration}ms"
    Write-BenchmarkLog "  Throughput: $throughput ops/sec"
    
    return $benchmark
}

function Save-BenchmarkResults {
    param([array]$Results)
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $resultsFile = Join-Path $script:BenchmarkResultsDir "benchmark_results_$timestamp.json"
    
    $benchmarkReport = @{
        "benchmark_session" = @{
            "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            "system_info" = @{
                "os" = (Get-WmiObject -Class Win32_OperatingSystem).Caption
                "processor" = (Get-WmiObject -Class Win32_Processor).Name
                "total_memory_gb" = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
            }
            "parameters" = @{
                "iterations" = $Iterations
                "concurrency" = $Concurrency
            }
        }
        "results" = $Results
    }
    
    $benchmarkReport | ConvertTo-Json -Depth 10 | Out-File -FilePath $resultsFile -Encoding UTF8
    
    Write-BenchmarkLog "Benchmark results saved to: $resultsFile" "SUCCESS"
    return $resultsFile
}

function Show-BenchmarkSummary {
    param([array]$Results)
    
    Write-Host "`n=== Performance Benchmark Summary ===" -ForegroundColor Cyan
    
    foreach ($result in $Results) {
        if ($result) {
            Write-Host "`n$($result.service.ToUpper()) Service - $($result.test_type)" -ForegroundColor Yellow
            Write-Host "  Success Rate: $($result.success_rate_percent)% ($($result.success_count)/$($result.iterations))" -ForegroundColor Green
            Write-Host "  Average Duration: $($result.avg_duration_ms)ms" -ForegroundColor White
            Write-Host "  Throughput: $($result.throughput_ops_per_sec) ops/sec" -ForegroundColor White
            
            if ($result.service -eq "rag") {
                Write-Host "  Average Draft Length: $($result.avg_draft_length) characters" -ForegroundColor White
            }
            
            if ($result.service -eq "end_to_end") {
                Write-Host "  Average Ingest: $($result.avg_ingest_duration_ms)ms" -ForegroundColor White
                Write-Host "  Average Draft: $($result.avg_draft_duration_ms)ms" -ForegroundColor White
            }
        }
    }
    
    Write-Host "`nDetailed results saved in: $script:BenchmarkResultsDir" -ForegroundColor Cyan
}

function Show-Help {
    Write-Host @"
AI-Assisted Email Response System - Performance Benchmarking

USAGE:
    .\benchmark_performance.ps1 [OPTIONS]

BENCHMARK TYPES:
    -Ingestion          Benchmark email ingestion service
    -RAG                Benchmark RAG draft generation service
    -EndToEnd           Benchmark complete workflow (ingestion + draft generation)
    -All                Run all benchmark types

OPTIONS:
    -Iterations <n>     Number of iterations per benchmark (default: 10)
    -Concurrency <n>    Concurrency level for tests (default: 1)
    -Verbose            Enable verbose output
    -Help               Show this help message

EXAMPLES:
    .\benchmark_performance.ps1 -All                           # Run all benchmarks
    .\benchmark_performance.ps1 -RAG -Iterations 20            # RAG benchmark with 20 iterations
    .\benchmark_performance.ps1 -EndToEnd -Verbose             # End-to-end with verbose output
    .\benchmark_performance.ps1 -Ingestion -Concurrency 5      # Concurrent ingestion test

REQUIREMENTS:
    - Ingestion service running on http://localhost:8080
    - RAG service running on http://localhost:8003
    - Services must be healthy and responsive

OUTPUT:
    Results saved to: $script:BenchmarkResultsDir
    Execution logs: $script:LogFile

"@ -ForegroundColor White
}

# Main execution logic
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "=== AI Email Assistant - Performance Benchmarking ===" -ForegroundColor Green
Write-BenchmarkLog "Benchmark execution started with parameters: $($PSBoundParameters | ConvertTo-Json -Compress)"

# Determine what to benchmark
$benchmarksToRun = @()
if ($All) {
    $benchmarksToRun = @("Ingestion", "RAG", "EndToEnd")
} else {
    if ($Ingestion) { $benchmarksToRun += "Ingestion" }
    if ($RAG) { $benchmarksToRun += "RAG" }
    if ($EndToEnd) { $benchmarksToRun += "EndToEnd" }
}

if ($benchmarksToRun.Count -eq 0) {
    Write-BenchmarkLog "No benchmark types specified. Use -Help for usage information." "WARN"
    exit 1
}

# Run benchmarks
$results = @()

foreach ($benchmarkType in $benchmarksToRun) {
    switch ($benchmarkType) {
        "Ingestion" {
            $result = Measure-IngestionPerformance -Iterations $Iterations -Concurrency $Concurrency
            if ($result) { $results += $result }
        }
        "RAG" {
            $result = Measure-RAGPerformance -Iterations $Iterations -Concurrency $Concurrency
            if ($result) { $results += $result }
        }
        "EndToEnd" {
            $result = Measure-EndToEndPerformance -Iterations $Iterations
            if ($result) { $results += $result }
        }
    }
}

# Save and display results
if ($results.Count -gt 0) {
    $resultsFile = Save-BenchmarkResults -Results $results
    Show-BenchmarkSummary -Results $results
} else {
    Write-BenchmarkLog "No benchmark results to save" "WARN"
}

Write-BenchmarkLog "Benchmark execution completed"
