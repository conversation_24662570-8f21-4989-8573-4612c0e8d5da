#!/bin/bash
# PostgreSQL 16 Setup Script for Linux/macOS
# AI-Assisted Email Response System

set -e

echo "Setting up PostgreSQL 16 with pgvector extension..."

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Ubuntu/Debian
    if command -v apt-get &> /dev/null; then
        echo "Installing PostgreSQL 16 on Ubuntu/Debian..."
        sudo apt-get update
        sudo apt-get install -y postgresql-16 postgresql-16-dev build-essential git
    # CentOS/RHEL
    elif command -v yum &> /dev/null; then
        echo "Installing PostgreSQL 16 on CentOS/RHEL..."
        sudo yum install -y postgresql16-server postgresql16-devel gcc git
        sudo postgresql-16-setup initdb
        sudo systemctl enable postgresql-16
        sudo systemctl start postgresql-16
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "Installing PostgreSQL 16 on macOS..."
    if command -v brew &> /dev/null; then
        brew install postgresql@16
        brew services start postgresql@16
    else
        echo "Homebrew not found. Please install Homebrew first."
        exit 1
    fi
fi

# Install pgvector extension
echo "Installing pgvector extension..."
cd /tmp
git clone --branch v0.5.1 https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install

# Start PostgreSQL service
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
fi

# Create database and user
echo "Creating database and user..."
sudo -u postgres createdb email_db
sudo -u postgres psql -c "CREATE USER email_user WITH PASSWORD 'email_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE email_db TO email_user;"

# Enable pgvector extension
echo "Enabling pgvector extension..."
sudo -u postgres psql -d email_db -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Create database configuration
CONFIG_DIR="$(dirname "$0")/../config"
mkdir -p "$CONFIG_DIR"
cat > "$CONFIG_DIR/database.env" << EOF
DATABASE_URL=postgresql://email_user:email_password@localhost:5432/email_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=email_db
POSTGRES_USER=email_user
POSTGRES_PASSWORD=email_password
EOF

echo "PostgreSQL 16 with pgvector setup complete!"
echo "Database configuration saved to: $CONFIG_DIR/database.env"
