# Development Environment Setup Script for Windows
# AI-Assisted Email Response System

param(
    [switch]$SkipPostgreSQL,
    [switch]$SkipRust,
    [switch]$SkipPython,
    [switch]$SkipNodeJS
)

Write-Host "Setting up AI Email Assistant Development Environment..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as administrator." -ForegroundColor Red
    exit 1
}

# Install Chocolatey if not present
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey package manager..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Install PostgreSQL 16
if (-not $SkipPostgreSQL) {
    Write-Host "Installing PostgreSQL 16..." -ForegroundColor Yellow
    choco install postgresql16 --params '/Password:email_password' -y
}

# Install Rust
if (-not $SkipRust) {
    Write-Host "Installing Rust toolchain..." -ForegroundColor Yellow
    if (!(Get-Command rustc -ErrorAction SilentlyContinue)) {
        choco install rust -y
        # Refresh environment variables
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
    } else {
        Write-Host "Rust already installed, updating..." -ForegroundColor Green
        rustup update
    }
}

# Install Python 3.11
if (-not $SkipPython) {
    Write-Host "Installing Python 3.11..." -ForegroundColor Yellow
    choco install python311 -y
}

# Install Node.js
if (-not $SkipNodeJS) {
    Write-Host "Installing Node.js..." -ForegroundColor Yellow
    choco install nodejs -y
}

# Install Git if not present
if (!(Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Git..." -ForegroundColor Yellow
    choco install git -y
}

Write-Host "Development environment setup complete!" -ForegroundColor Green
Write-Host "Please restart your terminal to ensure all PATH changes take effect." -ForegroundColor Yellow

# Create verification script
$verifyScript = @"
Write-Host "Verifying development environment..." -ForegroundColor Green

# Check Rust
if (Get-Command rustc -ErrorAction SilentlyContinue) {
    Write-Host "✓ Rust: " -NoNewline -ForegroundColor Green
    rustc --version
} else {
    Write-Host "✗ Rust not found" -ForegroundColor Red
}

# Check Python
if (Get-Command python -ErrorAction SilentlyContinue) {
    Write-Host "✓ Python: " -NoNewline -ForegroundColor Green
    python --version
} else {
    Write-Host "✗ Python not found" -ForegroundColor Red
}

# Check Node.js
if (Get-Command node -ErrorAction SilentlyContinue) {
    Write-Host "✓ Node.js: " -NoNewline -ForegroundColor Green
    node --version
} else {
    Write-Host "✗ Node.js not found" -ForegroundColor Red
}

# Check PostgreSQL
if (Get-Command psql -ErrorAction SilentlyContinue) {
    Write-Host "✓ PostgreSQL: " -NoNewline -ForegroundColor Green
    psql --version
} else {
    Write-Host "✗ PostgreSQL not found" -ForegroundColor Red
}
"@

$verifyScript | Out-File -FilePath "$(Split-Path $MyInvocation.MyCommand.Path)\verify_environment.ps1" -Encoding UTF8
