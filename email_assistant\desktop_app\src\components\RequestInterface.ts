// AI-Assisted Email Response System - Request Interface Component

import { invoke } from '@tauri-apps/api/core';
import type { RequestMode, ResponseContext, DraftResponse } from '../types';

export class RequestInterface {
  private container: HTMLElement;
  private currentMode: RequestMode['id'] = 'general';
  private currentContext: ResponseContext['id'] = 'general';
  private onDraftGenerated: ((draft: DraftResponse) => void) | null = null;

  private readonly modes: RequestMode[] = [
    {
      id: 'general',
      label: 'General Information Request',
      description: 'Ask general questions about your emails and cases (Information Mode)'
    },
    {
      id: 'case-specific',
      label: 'Case-Specific Information',
      description: 'Get information about a specific case or legal matter (Information Mode)'
    },
    {
      id: 'draft-reply',
      label: 'Draft Email Generator',
      description: 'Generate a draft email response (Draft Mode)'
    }
  ];

  private readonly contexts: ResponseContext[] = [
    {
      id: 'general',
      label: 'General Response',
      description: 'Standard professional response'
    },
    {
      id: 'lawyer-to-insurance',
      label: 'Lawyer → Insurance',
      description: 'Response from lawyer to insurance company'
    },
    {
      id: 'insurance-to-lawyer',
      label: 'Insurance → Lawyer',
      description: 'Response from insurance company to lawyer'
    }
  ];

  constructor(container: HTMLElement) {
    this.container = container;
    this.render();
    this.setupEventListeners();
  }

  public setOnDraftGenerated(callback: (draft: DraftResponse) => void) {
    this.onDraftGenerated = callback;
  }

  private render() {
    this.container.innerHTML = `
      <div class="request-interface-container">
        <div class="request-interface-header">
          <h2>AI Request Interface</h2>
          <div class="request-interface-controls">
            <div class="mode-selector">
              <label for="request-mode">Request Mode:</label>
              <select id="request-mode" class="mode-select">
                ${this.modes.map(mode => `
                  <option value="${mode.id}" ${mode.id === this.currentMode ? 'selected' : ''}>
                    ${mode.label}
                  </option>
                `).join('')}
              </select>
            </div>
            
            <div class="context-selector">
              <label for="response-context">Response Context:</label>
              <select id="response-context" class="context-select">
                ${this.contexts.map(context => `
                  <option value="${context.id}" ${context.id === this.currentContext ? 'selected' : ''}>
                    ${context.label}
                  </option>
                `).join('')}
              </select>
            </div>
          </div>
        </div>
        
        <div class="request-interface-body">
          <div class="mode-description">
            <p id="mode-description-text">${this.getModeDescription()}</p>
          </div>
          
          <div class="request-input-section">
            <div class="input-group">
              <label for="request-input">Your Request:</label>
              <textarea 
                id="request-input" 
                class="request-textarea" 
                placeholder="${this.getPlaceholderText()}"
                rows="4"
              ></textarea>
            </div>
            
            <div class="request-actions">
              <button id="submit-request" class="btn btn-primary">
                <span class="icon">✨</span>
                ${this.getSubmitButtonText()}
              </button>
              <button id="clear-request" class="btn btn-secondary">
                <span class="icon">🗑️</span>
                Clear
              </button>
            </div>
          </div>
          
          <div id="request-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Processing your request...</p>
          </div>
          
          <div id="request-error" class="error-state" style="display: none;"></div>
        </div>
      </div>
    `;
  }

  private setupEventListeners() {
    const modeSelect = this.container.querySelector('#request-mode') as HTMLSelectElement;
    const contextSelect = this.container.querySelector('#response-context') as HTMLSelectElement;
    const submitBtn = this.container.querySelector('#submit-request') as HTMLButtonElement;
    const clearBtn = this.container.querySelector('#clear-request') as HTMLButtonElement;
    const requestInput = this.container.querySelector('#request-input') as HTMLTextAreaElement;

    modeSelect?.addEventListener('change', () => {
      this.currentMode = modeSelect.value as RequestMode['id'];
      this.updateInterface();
    });

    contextSelect?.addEventListener('change', () => {
      this.currentContext = contextSelect.value as ResponseContext['id'];
      this.updateInterface();
    });

    submitBtn?.addEventListener('click', () => {
      this.handleSubmitRequest();
    });

    clearBtn?.addEventListener('click', () => {
      requestInput.value = '';
      this.hideError();
    });

    // Submit on Ctrl+Enter
    requestInput?.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.key === 'Enter') {
        this.handleSubmitRequest();
      }
    });
  }

  private updateInterface() {
    const descriptionEl = this.container.querySelector('#mode-description-text') as HTMLElement;
    const requestInput = this.container.querySelector('#request-input') as HTMLTextAreaElement;
    const submitBtn = this.container.querySelector('#submit-request') as HTMLButtonElement;

    if (descriptionEl) {
      descriptionEl.textContent = this.getModeDescription();
    }

    if (requestInput) {
      requestInput.placeholder = this.getPlaceholderText();
    }

    if (submitBtn) {
      const textNode = submitBtn.childNodes[submitBtn.childNodes.length - 1];
      if (textNode) {
        textNode.textContent = this.getSubmitButtonText();
      }
    }
  }

  private getModeDescription(): string {
    const mode = this.modes.find(m => m.id === this.currentMode);
    return mode?.description || '';
  }

  private getPlaceholderText(): string {
    switch (this.currentMode) {
      case 'general':
        return 'Ask any question about your emails, cases, or legal matters...';
      case 'case-specific':
        return 'Enter case details or ask about a specific legal matter...';
      case 'draft-reply':
        return 'Describe the type of reply you want to generate...';
      default:
        return 'Enter your request...';
    }
  }

  private getSubmitButtonText(): string {
    switch (this.currentMode) {
      case 'general':
        return 'Get Information';
      case 'case-specific':
        return 'Search Case';
      case 'draft-reply':
        return 'Generate Draft';
      default:
        return 'Submit';
    }
  }

  private async handleSubmitRequest() {
    const requestInput = this.container.querySelector('#request-input') as HTMLTextAreaElement;
    const requestText = requestInput.value.trim();

    if (!requestText) {
      this.showError('Please enter a request before submitting.');
      return;
    }

    this.setLoading(true);
    this.hideError();

    try {
      // Route to appropriate endpoint based on mode
      if (this.currentMode === 'draft-reply') {
        // Use draft generation for email drafts
        const response: DraftResponse = await invoke('generate_draft', {
          subject: `${this.currentMode} request`,
          sender: this.currentContext,
          content: requestText,
          responseContext: this.currentContext
        });

        if (this.onDraftGenerated) {
          this.onDraftGenerated(response);
        }
      } else {
        // Use information generation for information requests
        const response = await invoke('generate_information', {
          query: requestText,
          context: null,
          responseContext: this.currentContext
        });

        // Convert information response to draft response format for display
        const draftResponse: DraftResponse = {
          draft: response.information,
          context_emails_count: response.context_emails_count,
          similar_emails: response.similar_emails,
          metadata: {
            ...response.metadata,
            request_mode: this.currentMode,
            response_type: 'information'
          }
        };

        if (this.onDraftGenerated) {
          this.onDraftGenerated(draftResponse);
        }
      }

      // Clear the input after successful submission
      requestInput.value = '';
      
    } catch (error) {
      console.error('Failed to process request:', error);
      this.showError(`Failed to process request: ${error}`);
    } finally {
      this.setLoading(false);
    }
  }

  private setLoading(loading: boolean) {
    const loadingEl = this.container.querySelector('#request-loading') as HTMLElement;
    const submitBtn = this.container.querySelector('#submit-request') as HTMLButtonElement;

    if (loadingEl) {
      loadingEl.style.display = loading ? 'block' : 'none';
    }

    if (submitBtn) {
      submitBtn.disabled = loading;
    }
  }

  private showError(message: string) {
    const errorEl = this.container.querySelector('#request-error') as HTMLElement;
    if (errorEl) {
      errorEl.textContent = message;
      errorEl.style.display = 'block';
    }
  }

  private hideError() {
    const errorEl = this.container.querySelector('#request-error') as HTMLElement;
    if (errorEl) {
      errorEl.style.display = 'none';
    }
  }
}
