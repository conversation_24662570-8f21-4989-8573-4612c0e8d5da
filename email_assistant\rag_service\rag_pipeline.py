"""
RAG Pipeline for Email Draft Generation

This module implements the Retrieval-Augmented Generation pipeline that:
1. Takes an incoming email as input
2. Generates embeddings for the email content
3. Searches for similar historical emails using vector similarity
4. Composes a prompt with retrieved context
5. Generates a draft response using a local LLM (Ollama/Llama-3)

Compliance Features:
- Comprehensive audit logging for EU AI Act compliance
- GDPR pseudonymisation support for cloud service protection
- Transaction tracking for regulatory reporting
"""

import logging
import httpx
import time
from typing import List, Dict, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from sentence_transformers import SentenceTransformer
import numpy as np
from compliance_logger import get_compliance_logger
from pseudonymizer import get_pseudonymizer, pseudonymize_email_content, pseudonymize_retrieved_emails
from llm_providers import LLMProvider, create_llm_provider

logger = logging.getLogger(__name__)

class RAGPipeline:
    """RAG Pipeline for generating email draft responses"""
    
    def __init__(
        self,
        llm_provider: str = "ollama",
        llm_config: Optional[Dict[str, Any]] = None,
        ingestion_service_url: str = "http://localhost:8080",
        embedding_model_name: str = "intfloat/e5-large-v2",
        max_context_emails: int = 5,
        similarity_threshold: float = 0.7,
        # Legacy parameters for backward compatibility
        ollama_base_url: Optional[str] = None,
        ollama_model: Optional[str] = None
    ):
        """
        Initialize the RAG pipeline

        Args:
            llm_provider: LLM provider type ("ollama", "initium", "openai")
            llm_config: Configuration dict for the LLM provider
            ingestion_service_url: URL of the ingestion service for vector search
            embedding_model_name: Name of the sentence transformer model
            max_context_emails: Maximum number of similar emails to retrieve
            similarity_threshold: Minimum similarity score for retrieved emails
            ollama_base_url: Legacy parameter for backward compatibility
            ollama_model: Legacy parameter for backward compatibility
        """
        # Handle legacy parameters
        if ollama_base_url or ollama_model:
            llm_provider = "ollama"
            if not llm_config:
                llm_config = {}
            if ollama_base_url:
                llm_config["base_url"] = ollama_base_url
            if ollama_model:
                llm_config["model"] = ollama_model

        # Set default config if none provided
        if not llm_config:
            llm_config = {}

        self.llm_provider_type = llm_provider
        self.llm_config = llm_config
        self.ingestion_service_url = ingestion_service_url
        self.max_context_emails = max_context_emails
        self.similarity_threshold = similarity_threshold

        # Initialize embedding model
        self.embedding_model = SentenceTransformer(embedding_model_name)

        # Initialize LLM provider
        self.llm_provider = create_llm_provider(llm_provider, **llm_config)
        
        # Initialize HTTP client for ingestion service
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Initialize prompt templates
        self._create_prompt_templates()
        self.prompt_template = self.draft_prompt_template  # For backward compatibility
        
        # Initialize output parser
        self.output_parser = StrOutputParser()
        
        model_info = self.llm_provider.get_model_info()
        logger.info(f"RAG Pipeline initialized with {model_info['provider']} provider, model: {model_info['model']}")
    
    def _create_prompt_templates(self):
        """Create the RAG prompt templates for both email draft generation and information retrieval"""

        # Email draft generation template
        draft_template = """You are an AI assistant helping a legal advisor draft professional email responses.

{context_instruction}

Based on the following context of similar historical emails and responses, generate a professional draft response to the incoming email.

CONTEXT - Similar Historical Emails:
{context}

INCOMING EMAIL:
Subject: {subject}
From: {sender}
Content: {content}

INSTRUCTIONS:
1. Generate a professional, courteous response appropriate for a legal advisor
2. Use the context from similar historical emails to inform your response style and content
3. Address the specific points raised in the incoming email
4. Maintain a professional legal tone
5. Include appropriate legal disclaimers if relevant
6. Keep the response concise but comprehensive

EMAIL WEIGHTING GUIDANCE:
{weighting_instruction}

DRAFT RESPONSE:"""

        # Information retrieval template
        information_template = """You are an AI assistant helping a legal advisor retrieve and synthesize information.

{context_instruction}

Based on the following context of similar historical emails and information, provide a comprehensive answer to the query.

CONTEXT - Similar Historical Information:
{context}

QUERY: {query}

INSTRUCTIONS:
1. Provide a clear, factual response to the query
2. Use the context from similar historical emails to inform your response
3. Maintain a professional legal tone
4. Focus on information retrieval rather than drafting an email
5. Organize information in a structured, easy-to-read format
6. Cite specific examples from the context when relevant

INFORMATION WEIGHTING GUIDANCE:
{weighting_instruction}

RESPONSE:"""

        self.draft_prompt_template = PromptTemplate(
            input_variables=["context", "subject", "sender", "content", "context_instruction", "weighting_instruction"],
            template=draft_template
        )

        self.information_prompt_template = PromptTemplate(
            input_variables=["context", "query", "context_instruction", "weighting_instruction"],
            template=information_template
        )

    def _create_prompt_template(self) -> PromptTemplate:
        """Create the RAG prompt template for email draft generation (legacy method)"""
        self._create_prompt_templates()
        return self.draft_prompt_template
    
    async def generate_embeddings(self, text: str) -> List[float]:
        """Generate embeddings for the given text"""
        try:
            embeddings = self.embedding_model.encode(text)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise
    
    async def search_similar_emails(
        self,
        query_embedding: List[float],
        limit: int = None,
        thread_id: str = None,
        conversation_id: str = None
    ) -> List[Dict[str, Any]]:
        """Search for similar emails using vector similarity with optional thread prioritization"""
        try:
            limit = limit or self.max_context_emails

            # Use thread-aware search if thread context is provided
            if thread_id or conversation_id:
                # Call the new thread-aware search endpoint
                params = {
                    "q": "context_search",  # Placeholder query for embedding-based search
                    "limit": limit
                }
                if thread_id:
                    params["thread_id"] = thread_id
                if conversation_id:
                    params["conversation_id"] = conversation_id

                response = await self.http_client.get(
                    f"{self.ingestion_service_url}/search/thread",
                    params=params
                )
                response.raise_for_status()

                search_results = response.json()
                logger.info(f"Found {len(search_results)} thread-aware similar emails")

                return search_results
            else:
                # Fall back to standard search
                search_payload = {
                    "query_vector": query_embedding,
                    "limit": limit,
                    "score_threshold": self.similarity_threshold
                }

                response = await self.http_client.post(
                    f"{self.ingestion_service_url}/search_emails",
                    json=search_payload
                )
                response.raise_for_status()

                search_results = response.json()
                logger.info(f"Found {len(search_results)} similar emails")

                return search_results

        except Exception as e:
            logger.error(f"Error searching similar emails: {e}")
            return []

    async def get_conversation_thread(self, thread_id: str) -> List[Dict[str, Any]]:
        """Retrieve complete conversation thread"""
        try:
            response = await self.http_client.get(
                f"{self.ingestion_service_url}/conversations/{thread_id}"
            )
            response.raise_for_status()

            thread_data = response.json()
            logger.info(f"Retrieved conversation thread with {thread_data.get('total_count', 0)} emails")

            return thread_data.get('emails', [])

        except Exception as e:
            logger.error(f"Error retrieving conversation thread: {e}")
            return []

    async def get_case_emails(self, case_id: str) -> List[Dict[str, Any]]:
        """Retrieve emails for a specific case"""
        try:
            response = await self.http_client.get(
                f"{self.ingestion_service_url}/cases/{case_id}/emails"
            )
            response.raise_for_status()

            case_data = response.json()
            logger.info(f"Retrieved case emails with {case_data.get('total_count', 0)} emails")

            return case_data.get('emails', [])

        except Exception as e:
            logger.error(f"Error retrieving case emails: {e}")
            return []

    def _format_context(self, similar_emails: List[Dict[str, Any]]) -> str:
        """Format the retrieved emails into context for the prompt with email weighting"""
        if not similar_emails:
            return "No similar historical emails found."

        context_parts = []
        sent_emails = []
        received_emails = []

        # Separate sent and received emails for weighting
        for email in similar_emails:
            email_type = email.get('email_type', '').lower()
            if email_type == 'sent':
                sent_emails.append(email)
            else:
                received_emails.append(email)

        # Format sent emails first (higher weight)
        if sent_emails:
            context_parts.append("=== SENT EMAILS (Higher Priority for Style Reference) ===")
            for i, email in enumerate(sent_emails, 1):
                email_context = f"""
Sent Email {i}:
Subject: {email.get('subject', 'N/A')}
From: {email.get('from_address', 'N/A')}
Content: {email.get('preview', 'N/A')[:500]}...
Thread ID: {email.get('thread_id', 'N/A')}
---"""
                context_parts.append(email_context)

        # Format received emails second (lower weight)
        if received_emails:
            context_parts.append("\n=== RECEIVED EMAILS (Context Reference) ===")
            for i, email in enumerate(received_emails, 1):
                email_context = f"""
Received Email {i}:
Subject: {email.get('subject', 'N/A')}
From: {email.get('from_address', 'N/A')}
Content: {email.get('preview', 'N/A')[:500]}...
Thread ID: {email.get('thread_id', 'N/A')}
---"""
                context_parts.append(email_context)

        return "\n".join(context_parts)
    
    async def generate_draft(
        self,
        subject: str,
        sender: str,
        content: str,
        thread_id: str = None,
        conversation_id: str = None,
        email_context: str = None  # "lawyer_to_insurance", "insurance_to_lawyer", "general"
    ) -> Dict[str, Any]:
        """
        Generate a draft email response using RAG with comprehensive compliance logging

        Args:
            subject: Subject of the incoming email
            sender: Sender of the incoming email
            content: Content of the incoming email

        Returns:
            Dictionary containing the generated draft and metadata
        """
        # Initialize compliance logging
        compliance_logger = get_compliance_logger()
        transaction_id = compliance_logger.generate_transaction_id()
        start_time = time.time()

        try:
            logger.info(f"Generating draft for email from {sender} (Transaction: {transaction_id})")

            # Step 1: Log the incoming request
            request_data = {
                "subject": subject,
                "sender": sender,
                "content": content
            }
            compliance_logger.log_draft_request(transaction_id, request_data)

            # Step 2: Apply pseudonymisation if enabled (for potential cloud service calls)
            pseudonymizer = get_pseudonymizer()
            pseudo_subject, pseudo_sender, pseudo_content = pseudonymize_email_content(
                subject, sender, content
            )

            # Step 3: Generate embeddings for the incoming email
            email_text = f"Subject: {subject}\nContent: {content}"
            query_embedding = await self.generate_embeddings(email_text)

            # Log embedding generation
            embedding_info = {
                "dimension": len(query_embedding) if query_embedding else 0,
                "model": self.embedding_model.model_name if hasattr(self.embedding_model, 'model_name') else "unknown"
            }

            # Step 4: Search for similar historical emails with thread context
            similar_emails = await self.search_similar_emails(
                query_embedding,
                thread_id=thread_id,
                conversation_id=conversation_id
            )

            # Log context retrieval
            search_metadata = {
                "similarity_threshold": self.similarity_threshold,
                "max_context_emails": self.max_context_emails,
                "search_time_ms": 0  # Could be measured if needed
            }
            compliance_logger.log_context_retrieval(
                transaction_id, embedding_info, similar_emails, search_metadata
            )

            # Step 5: Apply pseudonymisation to retrieved emails if needed
            processed_similar_emails = similar_emails
            if pseudonymizer.config.enabled:
                processed_similar_emails = pseudonymize_retrieved_emails(similar_emails)

            # Step 6: Format context from retrieved emails
            context = self._format_context(processed_similar_emails)

            # Step 7: Compose the prompt with email context and weighting
            # Determine email context type for prompt
            context_type = email_context or "general"
            context_instruction = ""

            if context_type == "lawyer_to_insurance":
                context_instruction = (
                    "You are drafting an email from a lawyer to an insurance company. "
                    "Be detailed, formal, and precise. Include all relevant case details and legal references. "
                    "Maintain a professional tone throughout."
                )
            elif context_type == "insurance_to_lawyer":
                context_instruction = (
                    "You are drafting an email from an insurance company to a lawyer. "
                    "Be concise but thorough. Focus on policy details, claim status, and next steps. "
                    "Maintain a professional, business-like tone."
                )
            else:  # general
                context_instruction = (
                    "You are drafting a professional email response. "
                    "Maintain a balanced, professional tone appropriate for legal correspondence."
                )

            # Apply email weighting (66% sent, 34% inbox) by emphasizing sent emails in the prompt
            weighting_instruction = (
                "When drafting your response, pay special attention to the style, tone, and format "
                "of previous sent emails in the context (they are more representative of the desired output). "
                "Give approximately twice as much weight to sent emails as to received emails when determining "
                "the appropriate response style."
            )

            prompt = self.prompt_template.format(
                context=context,
                subject=subject,
                sender=sender,
                content=content,
                context_instruction=context_instruction,
                weighting_instruction=weighting_instruction
            )

            # Log prompt composition
            context_summary = {
                "email_count": len(similar_emails),
                "total_context_length": len(context),
                "pseudonymised": pseudonymizer.config.enabled
            }
            compliance_logger.log_prompt_composition(
                transaction_id, "legal_advisor_rag_v1", context_summary, len(prompt)
            )

            # Step 8: Generate draft using LLM
            logger.info("Calling LLM for draft generation...")
            llm_start_time = time.time()
            draft_response = await self._call_llm(prompt)
            llm_end_time = time.time()

            # Log LLM generation
            model_info = self.llm_provider.get_model_info()
            llm_metadata = {
                "provider": model_info["provider"],
                "model": model_info["model"],
                "generation_time_ms": int((llm_end_time - llm_start_time) * 1000)
            }
            generation_result = {
                "draft": draft_response,
                "success": True,
                "error": None
            }
            compliance_logger.log_llm_generation(transaction_id, llm_metadata, generation_result)

            # Step 9: Prepare response
            result = {
                "draft": draft_response,
                "context_emails_count": len(similar_emails),
                "similar_emails": similar_emails,  # Return original emails (not pseudonymised)
                "metadata": {
                    "provider": model_info["provider"],
                    "model": model_info["model"],
                    "similarity_threshold": self.similarity_threshold,
                    "max_context_emails": self.max_context_emails,
                    "transaction_id": transaction_id,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            }

            # Step 10: Log the final response
            processing_metadata = {
                "total_processing_time_ms": int((time.time() - start_time) * 1000),
                "embedding_model": embedding_info.get("model", "unknown"),
                "llm_provider": model_info["provider"],
                "llm_model": model_info["model"],
                "pseudonymisation_applied": pseudonymizer.config.enabled,
                "pseudonymisation_stats": pseudonymizer.get_replacement_stats()
            }
            compliance_logger.log_draft_response(transaction_id, result, processing_metadata)

            logger.info(f"Draft generation completed successfully (Transaction: {transaction_id})")
            return result

        except Exception as e:
            logger.error(f"Error generating draft (Transaction: {transaction_id}): {e}")

            # Log the error for compliance
            compliance_logger.log_error(
                transaction_id,
                "draft_generation_error",
                str(e),
                {
                    "subject": subject,
                    "sender": sender,
                    "content_length": len(content),
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            )
            raise

    async def generate_information(
        self,
        query: str,
        context: str = None,
        response_context: str = None
    ) -> Dict[str, Any]:
        """
        Generate information response using RAG with comprehensive compliance logging

        Args:
            query: Information query from the user
            context: Optional email context for information retrieval
            response_context: Response context type ("lawyer_to_insurance", "insurance_to_lawyer", "general")

        Returns:
            Dictionary containing the generated information and metadata
        """
        # Initialize compliance logging
        compliance_logger = get_compliance_logger()
        transaction_id = compliance_logger.generate_transaction_id()
        start_time = time.time()

        try:
            # Step 1: Log the information request
            request_data = {
                "query": query,
                "context": context,
                "response_context": response_context,
                "timestamp": time.time()
            }
            compliance_logger.log_draft_request(transaction_id, request_data)

            # Step 2: Initialize pseudonymizer
            pseudonymizer = get_pseudonymizer()

            # Step 3: Generate embeddings for the query
            query_text = f"Query: {query}"
            if context:
                query_text += f"\nContext: {context}"

            query_embedding = await self.generate_embeddings(query_text)

            # Log embedding generation
            embedding_info = {
                "dimension": len(query_embedding) if query_embedding else 0,
                "model": self.embedding_model.model_name if hasattr(self.embedding_model, 'model_name') else "unknown"
            }

            # Step 4: Search for similar historical emails
            similar_emails = await self.search_similar_emails(query_embedding)

            # Log context retrieval
            search_metadata = {
                "similarity_threshold": self.similarity_threshold,
                "max_context_emails": self.max_context_emails,
                "search_time_ms": 0
            }
            compliance_logger.log_context_retrieval(
                transaction_id, embedding_info, similar_emails, search_metadata
            )

            # Step 5: Apply pseudonymisation to retrieved emails if needed
            processed_similar_emails = similar_emails
            if pseudonymizer.config.enabled:
                processed_similar_emails = pseudonymize_retrieved_emails(similar_emails)

            # Step 6: Format context from retrieved emails
            context_text = self._format_context(processed_similar_emails)

            # Step 7: Compose the prompt with response context
            context_type = response_context or "general"
            context_instruction = ""

            if context_type == "lawyer_to_insurance":
                context_instruction = (
                    "You are providing information for a lawyer working with insurance companies. "
                    "Be detailed, precise, and include relevant legal references. "
                    "Focus on case details, legal precedents, and procedural information."
                )
            elif context_type == "insurance_to_lawyer":
                context_instruction = (
                    "You are providing information for insurance company communications with lawyers. "
                    "Focus on policy details, claim procedures, coverage information, and business processes. "
                    "Be thorough but business-focused."
                )
            else:  # general
                context_instruction = (
                    "You are providing general legal information. "
                    "Maintain a balanced, professional tone appropriate for legal research and information retrieval."
                )

            # Apply email weighting for information context
            weighting_instruction = (
                "When providing information, prioritize insights from sent emails in the context "
                "(they represent the organization's perspective and approach). "
                "Use received emails as supporting context and reference material."
            )

            prompt = self.information_prompt_template.format(
                context=context_text,
                query=query,
                context_instruction=context_instruction,
                weighting_instruction=weighting_instruction
            )

            # Log prompt composition
            context_summary = {
                "email_count": len(similar_emails),
                "total_context_length": len(context_text),
                "pseudonymised": pseudonymizer.config.enabled
            }
            compliance_logger.log_prompt_composition(
                transaction_id, "legal_advisor_information_v1", context_summary, len(prompt)
            )

            # Step 8: Generate information using LLM
            logger.info("Calling LLM for information generation...")
            llm_start_time = time.time()
            information_response = await self._call_llm(prompt)
            llm_end_time = time.time()

            # Log LLM generation
            model_info = self.llm_provider.get_model_info()
            llm_metadata = {
                "provider": model_info["provider"],
                "model": model_info["model"],
                "generation_time_ms": int((llm_end_time - llm_start_time) * 1000)
            }
            generation_result = {
                "information": information_response,
                "success": True,
                "error": None
            }
            compliance_logger.log_llm_generation(transaction_id, llm_metadata, generation_result)

            # Step 9: Prepare response
            result = {
                "information": information_response,
                "context_emails_count": len(similar_emails),
                "similar_emails": similar_emails,  # Return original emails (not pseudonymised)
                "metadata": {
                    "provider": model_info["provider"],
                    "model": model_info["model"],
                    "similarity_threshold": self.similarity_threshold,
                    "max_context_emails": self.max_context_emails,
                    "transaction_id": transaction_id,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "request_type": "information_retrieval"
                }
            }

            # Log successful completion
            compliance_logger.log_completion(
                transaction_id,
                "information_generation_success",
                {
                    "query": query,
                    "response_length": len(information_response),
                    "context_emails_used": len(similar_emails),
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            )

            logger.info(f"Information generation completed successfully (Transaction: {transaction_id})")
            return result

        except Exception as e:
            logger.error(f"Error generating information (Transaction: {transaction_id}): {e}")

            # Log the error for compliance
            compliance_logger.log_error(
                transaction_id,
                "information_generation_error",
                str(e),
                {
                    "query": query,
                    "context": context,
                    "response_context": response_context,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            )
            raise

    async def _call_llm(self, prompt: str) -> str:
        """Call the LLM to generate the draft response"""
        try:
            response = await self.llm_provider.generate(prompt)
            return response
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            # Fallback response
            return "I apologize, but I'm unable to generate a draft response at this time due to a technical issue. Please try again later."
    
    async def close(self):
        """Close the HTTP client"""
        await self.http_client.aclose()
