"""
Pseudonymisation Module for GDPR Compliance

This module provides pseudonymisation capabilities for the AI-Assisted Email Response System
to ensure GDPR compliance when processing personal data. It implements configurable
pseudonymisation rules and provides a foundation for advanced anonymization techniques.

Key Features:
- Configurable pseudonymisation rules via environment variables
- Pattern-based replacement for common PII (emails, names, phone numbers)
- Extensible architecture for future ML-based anonymization
- Reversible pseudonymisation with secure key management
- Integration with RAG pipeline for cloud service protection

GDPR Compliance Notes:
- Pseudonymisation reduces privacy risks but data remains personal data under GDPR
- This module prepares infrastructure for full anonymization when required
- Pseudonymised data can still be processed for legitimate business purposes
- Proper key management is essential for reversible pseudonymisation
"""

import re
import os
import hashlib
import uuid
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class PseudonymisationLevel(Enum):
    """Levels of pseudonymisation intensity"""
    DISABLED = "disabled"
    BASIC = "basic"          # Email addresses and obvious PII
    MODERATE = "moderate"    # Names, addresses, phone numbers
    AGGRESSIVE = "aggressive" # All potential identifiers


@dataclass
class PseudonymisationRule:
    """Configuration for a pseudonymisation rule"""
    name: str
    pattern: str
    replacement_template: str
    enabled: bool = True
    description: str = ""


class PseudonymisationConfig:
    """Configuration manager for pseudonymisation rules"""
    
    def __init__(self):
        self.level = PseudonymisationLevel(
            os.getenv("PSEUDONYMISATION_LEVEL", "disabled")
        )
        self.enabled = os.getenv("PSEUDONYMISATION_ENABLED", "false").lower() == "true"
        self.preserve_structure = os.getenv("PSEUDONYMISATION_PRESERVE_STRUCTURE", "true").lower() == "true"
        self.use_deterministic = os.getenv("PSEUDONYMISATION_DETERMINISTIC", "true").lower() == "true"
        
        # Load custom rules from environment
        self.custom_patterns = self._load_custom_patterns()
        
        # Initialize built-in rules
        self.rules = self._initialize_rules()
    
    def _load_custom_patterns(self) -> Dict[str, str]:
        """Load custom pseudonymisation patterns from environment variables"""
        patterns = {}
        
        # Look for environment variables like PSEUDO_PATTERN_EMAIL, PSEUDO_PATTERN_PHONE, etc.
        for key, value in os.environ.items():
            if key.startswith("PSEUDO_PATTERN_"):
                pattern_name = key.replace("PSEUDO_PATTERN_", "").lower()
                patterns[pattern_name] = value
        
        return patterns
    
    def _initialize_rules(self) -> List[PseudonymisationRule]:
        """Initialize built-in pseudonymisation rules"""
        rules = []
        
        if self.level in [PseudonymisationLevel.BASIC, PseudonymisationLevel.MODERATE, PseudonymisationLevel.AGGRESSIVE]:
            # Email addresses
            rules.append(PseudonymisationRule(
                name="email_addresses",
                pattern=r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b',
                replacement_template="[EMAIL_{hash}]",
                description="Replace email addresses with pseudonymised placeholders"
            ))
        
        if self.level in [PseudonymisationLevel.MODERATE, PseudonymisationLevel.AGGRESSIVE]:
            # Phone numbers (basic patterns)
            rules.append(PseudonymisationRule(
                name="phone_numbers",
                pattern=r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
                replacement_template="[PHONE_{hash}]",
                description="Replace phone numbers with pseudonymised placeholders"
            ))
            
            # Potential names (capitalized words, basic heuristic)
            rules.append(PseudonymisationRule(
                name="potential_names",
                pattern=r'\b[A-Z][a-z]{2,}\s+[A-Z][a-z]{2,}\b',
                replacement_template="[NAME_{hash}]",
                description="Replace potential names with pseudonymised placeholders"
            ))
        
        if self.level == PseudonymisationLevel.AGGRESSIVE:
            # Addresses (basic pattern)
            rules.append(PseudonymisationRule(
                name="addresses",
                pattern=r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr)',
                replacement_template="[ADDRESS_{hash}]",
                description="Replace addresses with pseudonymised placeholders"
            ))
            
            # Social Security Numbers (US format)
            rules.append(PseudonymisationRule(
                name="ssn",
                pattern=r'\b\d{3}-\d{2}-\d{4}\b',
                replacement_template="[SSN_{hash}]",
                description="Replace SSN with pseudonymised placeholders"
            ))
        
        return rules


class Pseudonymizer:
    """
    Main pseudonymisation engine
    
    Provides configurable pseudonymisation of text content to support GDPR compliance.
    Can be extended with advanced ML-based anonymization techniques.
    """
    
    def __init__(self, config: Optional[PseudonymisationConfig] = None):
        """
        Initialize the pseudonymiser
        
        Args:
            config: Pseudonymisation configuration, uses default if None
        """
        self.config = config or PseudonymisationConfig()
        self.replacement_map: Dict[str, str] = {}
        self.reverse_map: Dict[str, str] = {}
        
        # Generate a session key for deterministic pseudonymisation
        self.session_key = os.getenv("PSEUDONYMISATION_SESSION_KEY", str(uuid.uuid4()))
        
        logger.info(f"Pseudonymizer initialized with level: {self.config.level.value}, enabled: {self.config.enabled}")
    
    def pseudonymize_text(self, text: str) -> str:
        """
        Pseudonymise text content according to configured rules
        
        Args:
            text: Input text to pseudonymise
            
        Returns:
            Pseudonymised text with PII replaced by placeholders
        """
        if not self.config.enabled or self.config.level == PseudonymisationLevel.DISABLED:
            logger.debug("Pseudonymisation disabled, returning original text")
            return text
        
        if not text or not text.strip():
            return text
        
        pseudonymised_text = text
        replacements_made = 0
        
        # Apply each enabled rule
        for rule in self.config.rules:
            if not rule.enabled:
                continue
            
            try:
                # Find all matches for this pattern
                matches = re.finditer(rule.pattern, pseudonymised_text, re.IGNORECASE)
                
                for match in matches:
                    original_value = match.group(0)
                    
                    # Generate pseudonym
                    pseudonym = self._generate_pseudonym(original_value, rule.replacement_template)
                    
                    # Replace in text
                    pseudonymised_text = pseudonymised_text.replace(original_value, pseudonym)
                    replacements_made += 1
                    
                    logger.debug(f"Applied rule '{rule.name}': {original_value} -> {pseudonym}")
            
            except re.error as e:
                logger.error(f"Invalid regex pattern in rule '{rule.name}': {e}")
                continue
            except Exception as e:
                logger.error(f"Error applying rule '{rule.name}': {e}")
                continue
        
        if replacements_made > 0:
            logger.info(f"Pseudonymisation completed: {replacements_made} replacements made")
        else:
            logger.debug("No pseudonymisation replacements needed")
        
        return pseudonymised_text
    
    def _generate_pseudonym(self, original_value: str, template: str) -> str:
        """
        Generate a pseudonym for an original value
        
        Args:
            original_value: The original value to pseudonymise
            template: Template for the pseudonym (e.g., "[EMAIL_{hash}]")
            
        Returns:
            Generated pseudonym
        """
        if self.config.use_deterministic:
            # Use deterministic hashing for consistent pseudonyms
            hash_input = f"{self.session_key}:{original_value}".encode('utf-8')
            hash_value = hashlib.sha256(hash_input).hexdigest()[:8]
        else:
            # Use random UUID
            hash_value = str(uuid.uuid4())[:8]
        
        # Store mapping for potential reversal (if needed for debugging)
        pseudonym = template.format(hash=hash_value)
        
        if original_value not in self.replacement_map:
            self.replacement_map[original_value] = pseudonym
            self.reverse_map[pseudonym] = original_value
        
        return pseudonym
    
    def get_replacement_stats(self) -> Dict[str, Any]:
        """
        Get statistics about pseudonymisation replacements
        
        Returns:
            Dictionary with replacement statistics
        """
        return {
            "total_replacements": len(self.replacement_map),
            "config_level": self.config.level.value,
            "enabled": self.config.enabled,
            "rules_count": len([r for r in self.config.rules if r.enabled])
        }
    
    def clear_session(self) -> None:
        """Clear the current pseudonymisation session and mappings"""
        self.replacement_map.clear()
        self.reverse_map.clear()
        self.session_key = str(uuid.uuid4())
        logger.info("Pseudonymisation session cleared")


# Global pseudonymizer instance
_pseudonymizer: Optional[Pseudonymizer] = None


def get_pseudonymizer() -> Pseudonymizer:
    """Get the global pseudonymizer instance"""
    global _pseudonymizer
    
    if _pseudonymizer is None:
        _pseudonymizer = Pseudonymizer()
    
    return _pseudonymizer


def pseudonymize_text(text: str) -> str:
    """
    Convenience function to pseudonymise text using the global pseudonymizer
    
    Args:
        text: Text to pseudonymise
        
    Returns:
        Pseudonymised text
    """
    return get_pseudonymizer().pseudonymize_text(text)


def pseudonymize_email_content(subject: str, sender: str, content: str) -> Tuple[str, str, str]:
    """
    Pseudonymise email content (subject, sender, content)
    
    Args:
        subject: Email subject
        sender: Email sender
        content: Email content
        
    Returns:
        Tuple of (pseudonymised_subject, pseudonymised_sender, pseudonymised_content)
    """
    pseudonymizer = get_pseudonymizer()
    
    return (
        pseudonymizer.pseudonymize_text(subject),
        pseudonymizer.pseudonymize_text(sender),
        pseudonymizer.pseudonymize_text(content)
    )


def pseudonymize_retrieved_emails(emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Pseudonymise a list of retrieved emails
    
    Args:
        emails: List of email dictionaries
        
    Returns:
        List of pseudonymised email dictionaries
    """
    pseudonymizer = get_pseudonymizer()
    pseudonymised_emails = []
    
    for email in emails:
        pseudonymised_email = email.copy()
        
        # Pseudonymise common fields
        if "subject" in pseudonymised_email:
            pseudonymised_email["subject"] = pseudonymizer.pseudonymize_text(pseudonymised_email["subject"])
        
        if "content" in pseudonymised_email:
            pseudonymised_email["content"] = pseudonymizer.pseudonymize_text(pseudonymised_email["content"])
        
        if "sender" in pseudonymised_email:
            pseudonymised_email["sender"] = pseudonymizer.pseudonymize_text(pseudonymised_email["sender"])
        
        pseudonymised_emails.append(pseudonymised_email)
    
    return pseudonymised_emails
