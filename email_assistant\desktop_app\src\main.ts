import './style.css'
import { EmailList } from './components/EmailList'
import { EmailDetail } from './components/EmailDetail'
import { DraftDisplay } from './components/DraftDisplay'
import { RequestInterface } from './components/RequestInterface'
import { invoke } from '@tauri-apps/api/core'
import { open } from '@tauri-apps/plugin-dialog'
import type { ServiceHealthResponse, DraftResponse, EmailDetailResponse } from './types'

// Initialize the application
document.querySelector<HTMLDivElement>("#app")!.innerHTML = `
  <div class="app-container">
    <header class="app-header">
      <h1>AI-Assisted Email Response System</h1>
      <div class="header-controls">
        <div class="model-selection">
          <label for="model-select" class="model-label">LLM Model:</label>
          <select id="model-select" class="model-select">
            <option value="">Loading models...</option>
          </select>
          <button id="refresh-models-btn" class="btn btn-secondary btn-small" title="Refresh available models">
            🔄
          </button>
        </div>
        <div class="service-status">
          <div id="service-indicators" class="service-indicators">
            <div class="service-indicator" id="ingestion-status">
              <span class="indicator-dot"></span>
              <span class="indicator-label">Ingestion Service</span>
            </div>
            <div class="service-indicator" id="rag-status">
              <span class="indicator-dot"></span>
              <span class="indicator-label">RAG Service</span>
            </div>
          </div>
          <button id="file-upload-btn" class="btn btn-primary">
            <span class="icon">📁</span>
            Process Email Files
          </button>
        </div>
      </div>
    </header>

    <main class="app-main">
      <div class="app-layout">
        <div class="sidebar">
          <div id="email-list-container" class="email-list-section"></div>
        </div>
        
        <div class="main-content">
          <div class="content-top">
            <div id="email-detail-container" class="email-detail-section"></div>
          </div>

          <div class="content-middle">
            <div id="request-interface-container" class="request-interface-section"></div>
          </div>

          <div class="content-bottom">
            <div id="draft-display-container" class="draft-display-section"></div>
          </div>
        </div>
      </div>
    </main>
  </div>
`;

// Initialize components
const emailListContainer = document.querySelector("#email-list-container") as HTMLElement;
const emailDetailContainer = document.querySelector("#email-detail-container") as HTMLElement;
const requestInterfaceContainer = document.querySelector("#request-interface-container") as HTMLElement;
const draftDisplayContainer = document.querySelector("#draft-display-container") as HTMLElement;

const emailList = new EmailList(emailListContainer);
const emailDetail = new EmailDetail(emailDetailContainer);
const requestInterface = new RequestInterface(requestInterfaceContainer);
const draftDisplay = new DraftDisplay(draftDisplayContainer);

// Set up component interactions
emailList.setOnEmailSelect((email) => {
  emailDetail.loadEmail(email);
  // Clear any existing draft when selecting a new email
  draftDisplay.displayPlaceholder();
});

// Set up request interface to draft display interaction
requestInterface.setOnDraftGenerated((draft) => {
  draftDisplay.displayDraft(draft);
});

emailDetail.setOnGenerateDraft(async (_emailId) => {
  try {
    draftDisplay.setLoading(true);

    // Get the current email details
    const currentEmail = (emailDetail as any).email as EmailDetailResponse;
    if (!currentEmail) {
      throw new Error('No email selected');
    }

    // Prepare the draft request
    const subject = currentEmail.subject || '(No subject)';
    const sender = currentEmail.from_address || 'Unknown sender';
    const content = currentEmail.plain_text_content || '';

    if (!content.trim()) {
      throw new Error('Email content is empty');
    }

    // Get the current response context from EmailDetail
    const responseContext = (emailDetail as any).currentResponseContext || 'general';

    // Call the generate_draft Tauri command
    const draftResponse: DraftResponse = await invoke('generate_draft', {
      subject,
      sender,
      content,
      responseContext
    });

    // Display the generated draft
    draftDisplay.displayDraft(draftResponse);

  } catch (error) {
    console.error('Draft generation failed:', error);
    draftDisplay.displayError(`Failed to generate draft: ${error}`);
  } finally {
    draftDisplay.setLoading(false);
  }
});

// Set up information generation handler
emailDetail.setOnGenerateInformation(async (_emailId, responseContext) => {
  try {
    draftDisplay.setLoading(true);

    // Get the current email details
    const currentEmail = (emailDetail as any).email as EmailDetailResponse;
    if (!currentEmail) {
      throw new Error('No email selected');
    }

    // Prepare the information request
    const subject = currentEmail.subject || '(No subject)';
    const content = currentEmail.plain_text_content || '';
    const query = `Provide information about this email: Subject: ${subject}`;
    const context = content;

    if (!content.trim()) {
      throw new Error('Email content is empty');
    }

    // Call the generate_information Tauri command
    const informationResponse = await invoke('generate_information', {
      query,
      context,
      responseContext
    }) as any;

    // Display the generated information as text
    draftDisplay.displayDraftText(informationResponse.information, {
      contextEmailsCount: informationResponse.context_emails_count,
      similarEmails: informationResponse.similar_emails,
      metadata: informationResponse.metadata
    });

  } catch (error) {
    console.error('Information generation failed:', error);
    draftDisplay.displayError(`Failed to generate information: ${error}`);
  } finally {
    draftDisplay.setLoading(false);
  }
});

// Initialize with empty states
emailDetail.clear();
draftDisplay.displayPlaceholder();

// Set up file upload functionality
const fileUploadBtn = document.querySelector("#file-upload-btn") as HTMLButtonElement;
fileUploadBtn?.addEventListener('click', async () => {
  try {
    const selected = await open({
      multiple: true,
      filters: [{
        name: 'Email Files',
        extensions: ['eml', 'mbox']
      }]
    });
    
    if (selected && Array.isArray(selected)) {
      for (const filePath of selected) {
        await processEmailFile(filePath);
      }
      // Refresh the email list after processing
      emailList.refresh();
    } else if (selected) {
      await processEmailFile(selected);
      emailList.refresh();
    }
  } catch (error) {
    console.error('File selection failed:', error);
    alert(`File selection failed: ${error}`);
  }
});

async function processEmailFile(filePath: string) {
  try {
    const fileType = filePath.toLowerCase().endsWith('.mbox') ? 'mbox' : 'eml';
    const result = await invoke('process_email_file', {
      request: {
        file_path: filePath,
        file_type: fileType
      }
    });
    
    console.log('Processing result:', result);
    alert(`Successfully processed ${filePath}`);
  } catch (error) {
    console.error('Processing failed:', error);
    alert(`Failed to process ${filePath}: ${error}`);
  }
}

// Check service health on startup
async function checkServiceHealth() {
  try {
    // Check ingestion service
    const ingestionStatus = await invoke('check_service_health', {
      serviceUrl: 'http://localhost:8080'
    }) as ServiceHealthResponse;
    
    updateServiceIndicator('ingestion-status', ingestionStatus);
    
    // Check RAG service (placeholder for now)
    const ragStatus: ServiceHealthResponse = {
      service: 'http://localhost:8001',
      status: 'unavailable',
      message: 'RAG service not implemented yet'
    };
    
    updateServiceIndicator('rag-status', ragStatus);
    
  } catch (error) {
    console.error('Service health check failed:', error);
  }
}

function updateServiceIndicator(elementId: string, status: ServiceHealthResponse) {
  const indicator = document.querySelector(`#${elementId}`) as HTMLElement;
  if (!indicator) return;
  
  const dot = indicator.querySelector('.indicator-dot') as HTMLElement;
  
  // Remove existing status classes
  dot.classList.remove('status-healthy', 'status-unhealthy', 'status-error', 'status-unavailable');
  
  // Add appropriate status class
  switch (status.status) {
    case 'healthy':
      dot.classList.add('status-healthy');
      break;
    case 'unhealthy':
      dot.classList.add('status-unhealthy');
      break;
    case 'unavailable':
      dot.classList.add('status-unavailable');
      break;
    default:
      dot.classList.add('status-error');
  }
  
  // Update tooltip
  indicator.title = `${status.service}: ${status.message}`;
}

// Model selection functionality
async function loadAvailableModels() {
  try {
    const modelSelect = document.querySelector("#model-select") as HTMLSelectElement;
    if (!modelSelect) return;

    // Show loading state
    modelSelect.innerHTML = '<option value="">Loading models...</option>';
    modelSelect.disabled = true;

    // Get available models from RAG service
    const response = await fetch('http://localhost:8003/available_models');
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const models = data.models || [];

    // Clear and populate the select
    modelSelect.innerHTML = '';

    if (models.length === 0) {
      modelSelect.innerHTML = '<option value="">No models available</option>';
      return;
    }

    // Add models to select
    models.forEach((model: string) => {
      const option = document.createElement('option');
      option.value = model;
      option.textContent = model;
      modelSelect.appendChild(option);
    });

    // Set current model if available
    if (data.current_model) {
      modelSelect.value = data.current_model;
    }

    modelSelect.disabled = false;

  } catch (error) {
    console.error('Failed to load models:', error);
    const modelSelect = document.querySelector("#model-select") as HTMLSelectElement;
    if (modelSelect) {
      modelSelect.innerHTML = '<option value="">Failed to load models</option>';
      modelSelect.disabled = true;
    }
  }
}

async function changeModel(modelName: string) {
  try {
    const response = await fetch('http://localhost:8003/set_model', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ model: modelName })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Model changed successfully:', result);

    // Show success feedback
    const modelSelect = document.querySelector("#model-select") as HTMLSelectElement;
    if (modelSelect) {
      modelSelect.style.borderColor = '#4CAF50';
      setTimeout(() => {
        modelSelect.style.borderColor = '';
      }, 2000);
    }

  } catch (error) {
    console.error('Failed to change model:', error);
    alert(`Failed to change model: ${error}`);

    // Reload models to reset selection
    loadAvailableModels();
  }
}

// Set up model selection event handlers
const modelSelect = document.querySelector("#model-select") as HTMLSelectElement;
const refreshModelsBtn = document.querySelector("#refresh-models-btn") as HTMLButtonElement;

modelSelect?.addEventListener('change', (event) => {
  const target = event.target as HTMLSelectElement;
  if (target.value) {
    changeModel(target.value);
  }
});

refreshModelsBtn?.addEventListener('click', () => {
  loadAvailableModels();
});

// Initialize the application
checkServiceHealth();
loadAvailableModels();
