// Example: Testing the Thunderbird Processor with Generated Test Data
// This example demonstrates how to use the processor with synthetic test data

use std::fs;
use ingestion_service::thunderbird_processor::ThunderbirdProcessor;
use ingestion_service::test_data::TestDataGenerator;
use ingestion_service::enhanced_mbox::EnhancedMboxWriter;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Thunderbird Processor Test Example");
    println!("==================================");

    // Create a temporary directory for test data
    let temp_dir = tempfile::tempdir()?;
    let test_mbox_path = temp_dir.path().join("test_emails.mbox");
    let output_path = temp_dir.path().join("processed_emails.mbox");

    // Generate test data
    println!("1. Generating test email data...");
    let test_datasets = TestDataGenerator::generate_all_test_data();
    let mut all_test_emails = Vec::new();
    
    for dataset in test_datasets {
        all_test_emails.extend(dataset);
    }
    
    println!("   Generated {} test emails", all_test_emails.len());

    // Create a test mbox file with the generated data
    println!("2. Creating test mbox file...");
    create_test_mbox_file(&test_mbox_path, &all_test_emails)?;
    println!("   Created test mbox: {}", test_mbox_path.display());

    // Process the test data
    println!("3. Processing emails with Thunderbird Processor...");
    let mut processor = ThunderbirdProcessor::new();
    
    // For this example, we'll process the single test file
    // In real usage, you'd point to a Thunderbird directory
    let report = processor.process_thunderbird_directory(
        temp_dir.path().to_str().unwrap(),
        output_path.to_str().unwrap()
    )?;

    // Display results
    println!("4. Processing completed!");
    report.print_summary();

    // Verify the output
    println!("5. Verifying output file...");
    if output_path.exists() {
        let output_size = fs::metadata(&output_path)?.len();
        println!("   Output file size: {} bytes", output_size);
        
        // Read first few lines to show the enhanced format
        let content = fs::read_to_string(&output_path)?;
        let lines: Vec<&str> = content.lines().take(20).collect();
        
        println!("   First 20 lines of output:");
        for (i, line) in lines.iter().enumerate() {
            println!("   {:2}: {}", i + 1, line);
        }
    } else {
        println!("   ⚠ Output file was not created");
    }

    println!("\nTest completed successfully!");
    println!("Temporary files will be cleaned up automatically.");

    Ok(())
}

/// Create a test mbox file from threaded emails
fn create_test_mbox_file(
    file_path: &std::path::Path,
    emails: &[ingestion_service::email_threading::ThreadedEmail]
) -> Result<(), Box<dyn std::error::Error>> {
    let mut writer = EnhancedMboxWriter::new(file_path.to_str().unwrap())?;
    
    for email in emails {
        writer.write_email(email)?;
    }
    
    writer.close()?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_example_runs() {
        // This test ensures the example code compiles and basic functionality works
        let test_datasets = TestDataGenerator::generate_all_test_data();
        assert!(!test_datasets.is_empty());
        
        for dataset in &test_datasets {
            assert!(!dataset.is_empty());
        }
    }

    #[test]
    fn test_mbox_creation() -> Result<(), Box<dyn std::error::Error>> {
        let temp_file = tempfile::NamedTempFile::new()?;
        let test_emails = TestDataGenerator::create_simple_thread();
        
        create_test_mbox_file(temp_file.path(), &test_emails)?;
        
        // Verify file was created and has content
        let metadata = fs::metadata(temp_file.path())?;
        assert!(metadata.len() > 0);
        
        Ok(())
    }
}
